# School Database Integration

## Overview

The Lighten Counsel application now includes a comprehensive school database integration powered by the College Scorecard API. This system provides access to 6,300+ schools with essential information for college application planning.

## Features

### 1. College Scorecard API Integration
- **Data Source**: U.S. Department of Education College Scorecard API
- **API Key**: Configured in environment variables
- **Data Coverage**: 6,300+ schools with comprehensive information
- **Update Mechanism**: On-demand population via API endpoints

### 2. Database Schema
The `schools` table includes the following fields:
- `id` (UUID): Primary key
- `scorecard_id` (INTEGER): Unique College Scorecard identifier
- `name` (TEXT): School name
- `city`, `state`, `zip` (TEXT): Location information
- `school_url` (TEXT): Official website
- `ownership` (INTEGER): 1=Public, 2=Private nonprofit, 3=Private for-profit
- `admission_rate` (DECIMAL): Admission rate (0-1)
- `tuition_in_state`, `tuition_out_of_state` (INTEGER): Tuition costs
- `student_size` (INTEGER): Total enrollment
- `offers_bachelors` (BOOLEAN): Bachelor's degree availability
- `created_at`, `updated_at` (TIMESTAMP): Record timestamps

### 3. API Endpoints

#### Population Endpoint
- **URL**: `POST /api/populate-schools`
- **Purpose**: Fetch and store school data from College Scorecard API
- **Parameters**:
  - `maxPages` (optional): Number of pages to fetch
  - `perPage` (optional): Schools per page (default: 100)
  - `startPage` (optional): Starting page number
- **Example**: `POST /api/populate-schools?maxPages=10&perPage=100`

#### Search Endpoint
- **URL**: `GET /api/schools/search`
- **Purpose**: Search schools with filters and pagination
- **Parameters**:
  - `q` (optional): Search query for school names
  - `state` (optional): Filter by state (e.g., "CA", "NY")
  - `ownership` (optional): Filter by ownership type (1, 2, or 3)
  - `bachelors` (optional): Filter schools offering bachelor's degrees
  - `limit` (optional): Number of results (default: 20)
  - `offset` (optional): Pagination offset
- **Example**: `GET /api/schools/search?state=CA&limit=10&q=University`

#### Individual School Endpoints
- **Authenticated**: `GET /api/schools/[id]` (requires authentication)
- **Public**: `GET /api/schools/public/[id]` (no authentication required)
- **Purpose**: Get detailed information about a specific school

### 4. Frontend Components

#### SchoolSearch Component
- **Location**: `src/components/schools/school-search.tsx`
- **Features**:
  - Real-time search with debouncing
  - Advanced filtering (state, ownership, bachelor's programs)
  - Pagination with "Load More" functionality
  - Responsive design with loading states
  - Optional navigation to school detail pages

#### SchoolSelectorDialog Component
- **Location**: `src/components/schools/school-selector-dialog.tsx`
- **Features**:
  - Modal dialog for school selection
  - Multi-select or single-select modes
  - Maximum selection limits
  - Selected schools management
  - Integration with SchoolSearch component

#### School Detail Page
- **Location**: `src/app/(dashboard)/schools/[id]/page.tsx`
- **Features**:
  - Comprehensive school information display
  - Formatted tuition and admission data
  - External website links
  - Action buttons for adding to school lists

### 5. Navigation Integration
The school database is accessible through the main navigation for all user roles:
- **Students**: "Schools" menu item
- **Consultants**: "Schools Database" menu item
- **Admins**: "Schools Management" menu item

All navigation items point to `/schools` which provides the main school search interface.

## Usage Examples

### Basic School Search
```typescript
import { SchoolSearch } from '@/components/schools';

function MyComponent() {
  return (
    <SchoolSearch
      enableNavigation={true}
      placeholder="Search for colleges..."
      onSchoolSelect={(school) => {
        console.log('Selected:', school.name);
      }}
    />
  );
}
```

### School Selection Dialog
```typescript
import { SchoolSelectorDialog } from '@/components/schools';

function MyComponent() {
  const [selectedSchools, setSelectedSchools] = useState([]);
  
  return (
    <SchoolSelectorDialog
      selectedSchools={selectedSchools}
      onSchoolsChange={setSelectedSchools}
      maxSelections={10}
      triggerText="Add Target Schools"
    />
  );
}
```

### API Usage
```bash
# Search for California universities
curl "http://localhost:3001/api/schools/search?state=CA&q=University&limit=5"

# Get specific school details
curl "http://localhost:3001/api/schools/public/[school-id]"

# Populate database with more schools
curl -X POST "http://localhost:3001/api/populate-schools?maxPages=5&perPage=100"
```

## Data Management

### Current Status
- **Total Schools**: 6,300+ institutions
- **Data Source**: College Scorecard API (U.S. Department of Education)
- **Last Updated**: Populated on-demand via API
- **Coverage**: All operational U.S. colleges and universities

### Updating Data
To refresh or expand the school database:
1. Use the population endpoint: `POST /api/populate-schools`
2. Monitor server logs for progress
3. Check total count via search endpoint

### Performance Considerations
- Database includes proper indexes for efficient searching
- Search queries are optimized with pagination
- Frontend components use debouncing to reduce API calls
- Loading states provide good user experience

## Integration Points

### Student Application Flow
- Target school selection
- Safety/reach school categorization
- Application status tracking
- School comparison features

### Consultant Tools
- Student school list review
- Recommendation generation
- Application strategy planning

### Admin Functions
- School database management
- Data quality monitoring
- System analytics

## Future Enhancements

### Planned Features
1. **Advanced Filtering**: SAT/ACT score ranges, program-specific filters
2. **School Comparison**: Side-by-side comparison tool
3. **Favorites System**: Personal school bookmarking
4. **Application Integration**: Direct connection to application forms
5. **Data Enrichment**: Additional school information and rankings
6. **Automated Updates**: Scheduled data refresh from College Scorecard API

### Technical Improvements
1. **Caching**: Redis caching for frequently accessed schools
2. **Search Enhancement**: Elasticsearch integration for advanced search
3. **Data Validation**: Automated data quality checks
4. **Performance**: Database query optimization
5. **Monitoring**: School data freshness tracking

## Troubleshooting

### Common Issues
1. **Empty Search Results**: Check filter combinations, clear filters if needed
2. **Slow Loading**: Reduce search limit, check network connection
3. **School Not Found**: Verify school ID, use search to find correct school
4. **API Errors**: Check server logs, verify API key configuration

### Maintenance
- Monitor database size and performance
- Regular data updates from College Scorecard API
- Index maintenance for optimal search performance
- Log monitoring for API usage and errors

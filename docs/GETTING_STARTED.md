# Getting Started with Lighten Counsel

**Last Updated**: July 7, 2025

## 🚀 Quick Start Guide

This guide will help you set up and run Lighten Counsel locally for development or production deployment.

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** 18+ and **pnpm** (recommended package manager)
- **Git** for version control
- **Google Workspace** account with admin access (for Google Docs integration)
- **Supabase** account (for database)
- **Clerk** account (for authentication)

## 🛠️ Installation

### 1. Clone the Repository

```bash
git clone https://github.com/jierm2/lighten-Counsel.git
cd lighten-counsel
```

### 2. Install Dependencies

```bash
pnpm install
```

### 3. Environment Configuration

Create a `.env.local` file by copying the example:

```bash
cp env.example.txt .env.local
```

### 4. Configure Environment Variables

Edit `.env.local` with your configuration:

```env
# Next.js Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/dashboard
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/dashboard

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Google Workspace Integration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_PRIVATE_KEY=your_google_private_key
GOOGLE_CLIENT_EMAIL=your_service_account_email
GOOGLE_ADMIN_EMAIL=your_admin_email
```

## 🗄️ Database Setup

### 1. Create Supabase Project

1. Go to [Supabase](https://supabase.com) and create a new project
2. Note your project URL and API keys

### 2. Run Database Migrations

```bash
# Navigate to database directory
cd database

# Run the schema setup
psql -h your-supabase-host -U postgres -d postgres -f schema.sql

# Run RLS policies
psql -h your-supabase-host -U postgres -d postgres -f rls-policies.sql

# Add sample data (optional)
psql -h your-supabase-host -U postgres -d postgres -f sample-data.sql
```

## 🔐 Authentication Setup

### 1. Configure Clerk

1. Create a [Clerk](https://clerk.com) account and application
2. Configure OAuth providers if needed
3. Set up user roles: `student`, `consultant`, `admin`
4. Add your Clerk keys to `.env.local`

### 2. Configure User Roles

In your Clerk dashboard:
1. Go to Users → User Management
2. Set up custom user metadata for roles
3. Configure role-based access control

## 🔗 Google Workspace Integration

### 1. Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select existing one
3. Enable Google Docs API and Google Drive API

### 2. Create Service Account

1. Go to IAM & Admin → Service Accounts
2. Create a new service account
3. Download the JSON key file
4. Extract credentials for your `.env.local`

### 3. Configure Domain-Wide Delegation

1. Go to Google Workspace Admin Console
2. Navigate to Security → API Controls → Domain-wide Delegation
3. Add your service account Client ID with required scopes:
   - `https://www.googleapis.com/auth/documents`
   - `https://www.googleapis.com/auth/drive`

## 🚀 Running the Application

### Development Mode

```bash
pnpm dev
```

The application will be available at `http://localhost:3000`

### Production Build

```bash
pnpm build
pnpm start
```

### Development Commands

```bash
pnpm dev          # Start development server with Turbopack
pnpm build        # Production build
pnpm start        # Start production server
pnpm lint         # Run ESLint
pnpm format       # Format code with Prettier
```

## 🧪 Testing the Setup

### 1. Test Authentication

1. Navigate to `http://localhost:3000`
2. Sign up for a new account
3. Verify role assignment works

### 2. Test Google Docs Integration

1. Create a new document in the application
2. Verify Google Doc is created and accessible
3. Test collaborative editing features

### 3. Test Database Connection

1. Add test data through the application
2. Verify data persistence
3. Test role-based data access

## 📚 Next Steps

Once your setup is complete:

1. **Read the User Guide**: See [USER_GUIDE.md](./USER_GUIDE.md) for detailed usage instructions
2. **Review Architecture**: Check [ARCHITECTURE.md](./ARCHITECTURE.md) for system overview
3. **API Documentation**: Explore [API_DOCUMENTATION.md](./API_DOCUMENTATION.md) for API details
4. **Contributing**: See [CONTRIBUTING.md](./CONTRIBUTING.md) if you plan to contribute

## 🆘 Troubleshooting

### Common Issues

**Build Errors**
- Ensure Node.js 18+ is installed
- Clear `node_modules` and reinstall: `rm -rf node_modules && pnpm install`

**Authentication Issues**
- Verify Clerk keys are correct
- Check user role configuration

**Google Docs Integration Issues**
- Verify service account setup
- Check domain-wide delegation configuration
- See [GOOGLE_WORKSPACE_SETUP.md](./GOOGLE_WORKSPACE_SETUP.md) for detailed troubleshooting

**Database Connection Issues**
- Verify Supabase credentials
- Check RLS policies are properly configured
- Ensure service role key has proper permissions

For more detailed troubleshooting, see [TROUBLESHOOTING.md](./TROUBLESHOOTING.md).

## 📞 Support

If you encounter issues:

1. Check the [troubleshooting guide](./TROUBLESHOOTING.md)
2. Review the [GitHub issues](https://github.com/jierm2/lighten-Counsel/issues)
3. Create a new issue with detailed information

---

**Next**: [User Guide](./USER_GUIDE.md) | [Architecture Overview](./ARCHITECTURE.md) | [API Documentation](./API_DOCUMENTATION.md)

# Lighten Counsel - API Reference

**Last Updated**: July 7, 2025
**Base URL**: `/api`
**Authentication**: Clerk session tokens required

## 🔐 Authentication

All API endpoints require authentication via <PERSON>. The system uses role-based access control with three user roles:

- **Student**: Access to own data and documents
- **Consultant**: Access to assigned students' data
- **Admin**: Full system access

### Authentication Header

```http
Authorization: Bearer <clerk_session_token>
```

### Base URL

```
https://your-domain.com/api
```

## 📄 Core API Endpoints

### Document Management

#### List Documents

Get documents with role-based filtering and pagination.

```http
GET /api/documents
```

**Query Parameters:**

- `page` (optional): Page number for pagination
- `limit` (optional): Number of documents per page
- `type` (optional): Filter by document type (essay, transcript, etc.)

**Response:**

```json
{
  "success": true,
  "data": {
    "documents": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

**Query Parameters:**

- `student_id` (optional): Filter by specific student
- `doc_type` (optional): Filter by document type
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "student_id": "uuid",
      "google_doc_id": "google_doc_id",
      "doc_type": "essay",
      "metadata": {
        "title": "Harvard Diversity Essay",
        "word_limit": 650,
        "deadline": "2024-01-01",
        "status": "draft",
        "google_doc_url": "https://docs.google.com/document/d/..."
      },
      "created_at": "2024-01-01T00:00:00Z",
      "student_name": "John Smith",
      "consultant_emails": ["<EMAIL>"]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  }
}
```

### Create Document

Create a new document with Google Docs integration.

```http
POST /api/documents
```

**Request Body:**

```json
{
  "student_id": "uuid",
  "doc_type": "essay",
  "metadata": {
    "title": "Harvard Diversity Essay",
    "description": "Essay about diversity and inclusion",
    "word_limit": 650,
    "deadline": "2024-01-01",
    "school_id": "harvard_uuid"
  },
  "template_id": "template_uuid",
  "initial_content": "Start writing here..."
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "google_doc_id": "google_doc_id",
    "metadata": {
      "title": "Harvard Diversity Essay",
      "google_doc_url": "https://docs.google.com/document/d/...",
      "folder_id": "google_folder_id"
    },
    "student_name": "John Smith",
    "consultant_emails": ["<EMAIL>"]
  }
}
```

### Get Document Details

Get detailed information about a specific document.

```http
GET /api/documents/{id}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "student_id": "uuid",
    "google_doc_id": "google_doc_id",
    "doc_type": "essay",
    "metadata": {
      "title": "Harvard Diversity Essay",
      "word_limit": 650,
      "word_count": 425,
      "character_count": 2850,
      "last_synced": "2024-01-01T12:00:00Z",
      "google_doc_url": "https://docs.google.com/document/d/..."
    },
    "student_name": "John Smith",
    "consultant_emails": ["<EMAIL>"]
  }
}
```

### Delete Document

Delete a document (archives the Google Doc).

```http
DELETE /api/documents/{id}
```

**Response:**

```json
{
  "success": true,
  "message": "Document deleted successfully"
}
```

### Get Google Docs Content

Get the content and metadata from the associated Google Doc.

```http
GET /api/documents/{id}/google-doc
```

**Response:**

```json
{
  "success": true,
  "data": {
    "google_doc_id": "google_doc_id",
    "content": "Document content as plain text...",
    "metadata": {
      "title": "Harvard Diversity Essay",
      "google_doc_url": "https://docs.google.com/document/d/...",
      "word_count": 425,
      "character_count": 2850,
      "last_synced": "2024-01-01T12:00:00Z"
    }
  }
}
```

### Create Google Doc for Existing Document

Create a Google Doc for a document that doesn't have one yet.

```http
POST /api/documents/{id}/google-doc
```

**Request Body:**

```json
{
  "title": "Document Title",
  "content": "Initial content..."
}
```

### Bulk Document Operations

Perform bulk operations on multiple documents.

```http
POST /api/documents/operations
```

**Request Body:**

```json
{
  "operation": "sync_content",
  "document_ids": ["uuid1", "uuid2", "uuid3"]
}
```

**Available Operations:**

- `bulk_create`: Create multiple documents
- `bulk_permission_repair`: Repair permissions for multiple students
- `sync_content`: Sync content from Google Docs
- `export_documents`: Export documents in specified format

### Document Statistics

Get comprehensive document analytics.

```http
GET /api/documents/stats?student_id=uuid
```

**Response:**

```json
{
  "success": true,
  "data": {
    "total_documents": 15,
    "documents_with_google_docs": 12,
    "google_docs_integration_percentage": 80,
    "documents_by_type": {
      "essay": 8,
      "personal_statement": 3,
      "activity_resume": 2,
      "transcript": 2
    },
    "completion_stats": {
      "draft": 5,
      "in_review": 7,
      "completed": 3
    },
    "word_count_stats": {
      "total_words": 8500,
      "average_words": 567,
      "documents_with_word_count": 12
    },
    "recent_activity": [
      {
        "id": "uuid",
        "title": "Stanford Essay",
        "doc_type": "essay",
        "created_at": "2024-01-01T00:00:00Z",
        "has_google_doc": true
      }
    ]
  }
}
```

## Template Management API

### List Templates

Get available templates with filtering and metadata.

```http
GET /api/templates?doc_type=essay&category=competitive
```

**Query Parameters:**

- `doc_type` (optional): Filter by document type
- `category` (optional): Filter by template category

**Response:**

```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "id": "template_uuid",
        "name": "Personal Statement Template - Competitive Schools",
        "description": "Template for competitive college personal statements",
        "category": "competitive",
        "doc_type": "personal_statement",
        "usage_count": 45,
        "difficulty_level": "advanced",
        "word_limit": 650,
        "tags": ["competitive", "personal", "narrative"],
        "is_featured": true,
        "preview_url": "https://docs.google.com/document/d/.../preview"
      }
    ],
    "templates_by_category": {
      "competitive": [...],
      "general": [...]
    },
    "statistics": {
      "total_templates": 25,
      "most_used": [...],
      "featured": [...]
    }
  }
}
```

### Create Template

Create a new template (Admin only).

```http
POST /api/templates
```

**Request Body:**

```json
{
  "name": "New Essay Template",
  "content": "Template content...",
  "doc_type": "essay",
  "category": "general",
  "description": "Template description",
  "word_limit": 500,
  "tags": ["essay", "general"],
  "difficulty_level": "intermediate",
  "is_featured": false
}
```

### Get Template Recommendations

Get personalized template recommendations for a student.

```http
GET /api/templates/recommendations?student_id=uuid&doc_type=essay&limit=5
```

**Response:**

```json
{
  "success": true,
  "data": {
    "recommendations": [
      {
        "template_id": "uuid",
        "name": "Diversity Essay Template",
        "description": "Template for diversity-focused essays",
        "category": "competitive",
        "match_score": 8.5,
        "reasons": [
          "Matches your advanced level",
          "Suitable for competitive schools"
        ]
      }
    ],
    "context": {
      "student_name": "John Smith",
      "academic_level": "12th Grade",
      "target_school_count": 8,
      "existing_documents_count": 3
    }
  }
}
```

### Template Analytics

Get template usage analytics (Admin only).

```http
GET /api/templates/analytics
```

**Response:**

```json
{
  "success": true,
  "data": {
    "total_templates": 25,
    "total_usage": 450,
    "most_popular": [
      {
        "template_id": "uuid",
        "name": "Personal Statement Template",
        "usage_count": 85,
        "category": "general"
      }
    ],
    "usage_by_type": {
      "essay": 200,
      "personal_statement": 150,
      "activity_resume": 100
    },
    "recent_usage": [
      {
        "template_name": "Harvard Essay Template",
        "student_name": "John Smith",
        "used_at": "2024-01-01T12:00:00Z"
      }
    ]
  }
}
```

## Permission Management API

### Get Permission Summary

Get system-wide permission summary (Admin only).

```http
GET /api/permissions
```

**Response:**

```json
{
  "success": true,
  "data": {
    "total_students": 150,
    "total_documents": 450,
    "documents_with_google_docs": 380,
    "active_consultant_assignments": 120,
    "recent_permission_changes": [
      {
        "student_id": "uuid",
        "student_name": "John Smith",
        "action": "consultant_assigned",
        "timestamp": "2024-01-01T12:00:00Z"
      }
    ]
  }
}
```

### Permission Operations

Perform various permission management operations.

```http
POST /api/permissions
```

**Audit Student Permissions:**

```json
{
  "action": "audit",
  "student_id": "uuid"
}
```

**Repair Student Permissions:**

```json
{
  "action": "repair",
  "student_id": "uuid"
}
```

**Handle Consultant Assignment Change:**

```json
{
  "action": "consultant_change",
  "student_id": "uuid",
  "old_consultant_id": "uuid",
  "new_consultant_id": "uuid"
}
```

**Handle Consultant Deactivation:**

```json
{
  "action": "consultant_deactivation",
  "consultant_id": "uuid"
}
```

# Lighten Counsel - College Application Management System

A comprehensive college application management platform built with Next.js, designed to streamline the college application process for students, consultants, and administrators.

## 🚀 Current Status - PRODUCTION READY 🎉

**Version**: 1.1.0-dev
**Status**: ✅ Production Ready with Post-Production Optimization in Progress
**Last Updated**: July 7, 2025

### ✅ **Core Functionality Verified**

- **Google Docs Integration**: Fully operational document creation and collaboration
- **Role-Based Access Control**: Student, consultant, and admin dashboards working perfectly
- **Real Data Integration**: All mock data replaced with live database connections
- **Academic Records Management**: Complete GPA, test scores, and transcript management
- **Application Tracking**: Target schools, deadlines, and progress monitoring
- **Document Management**: Full CRUD operations with Google Drive integration

### 🎯 **Key Features**

- **For Students**: Essay management, academic records, activity portfolios, school applications
- **For Consultants**: Multi-student dashboard, document collaboration, progress tracking
- **For Administrators**: User management, system analytics, document oversight

## 🛠️ **Technology Stack**

### Frontend

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript 5.7.2
- **Styling**: Tailwind CSS 4.0.0 with shadcn/ui components
- **State Management**: Zustand 5.0.2
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React, Radix UI Icons

### Backend

- **API**: Next.js API Routes
- **Authentication**: Clerk with role-based access control
- **Database**: Supabase (PostgreSQL) with Row Level Security
- **File Storage**: Google Drive integration
- **Document Collaboration**: Google Docs API

### External Integrations

- **Google Workspace**: Docs API, Drive API, Admin SDK
- **Authentication**: Clerk for user management
- **Database**: Supabase for data persistence
- **Deployment**: Vercel (recommended)

## 🚀 **Quick Start**

| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Signup / Signin](https://go.clerk.com/ILdYhn7) | Authentication with **Clerk** provides secure authentication and user management with multiple sign-in options including passwordless authentication, social logins, and enterprise SSO - all designed to enhance security while delivering a seamless user experience. |
| [Dashboard (Overview)](https://shadcn-dashboard.kiranism.dev/dashboard) | Cards with Recharts graphs for analytics. Parallel routes in the overview sections feature independent loading, error handling, and isolated component rendering. |
| [Product](https://shadcn-dashboard.kiranism.dev/dashboard/product) | Tanstack tables with server side searching, filter, pagination by Nuqs which is a Type-safe search params state manager in nextjs |
| [Product/new](https://shadcn-dashboard.kiranism.dev/dashboard/product/new) | A Product Form with shadcn form (react-hook-form + zod). |
| [Profile](https://shadcn-dashboard.kiranism.dev/dashboard/profile) | Clerk's full-featured account management UI that allows users to manage their profile and security settings |
| [Kanban Board](https://shadcn-dashboard.kiranism.dev/dashboard/kanban) | A Drag n Drop task management board with dnd-kit and zustand to persist state locally. |
| [Not Found](https://shadcn-dashboard.kiranism.dev/dashboard/notfound) | Not Found Page Added in the root level |
| [Global Error](https://sentry.io/for/nextjs/?utm_source=github&utm_medium=paid-community&utm_campaign=general-fy26q2-nextjs&utm_content=github-banner-project-tryfree) | A centralized error page that captures and displays errors across the application. Integrated with **Sentry** to log errors, provide detailed reports, and enable replay functionality for better debugging. |

## Feature based organization

```plaintext
src/
├── app/ # Next.js App Router directory
│ ├── (auth)/ # Auth route group
│ │ ├── (signin)/
│ ├── (dashboard)/ # Dashboard route group
│ │ ├── layout.tsx
│ │ ├── loading.tsx
│ │ └── page.tsx
│ └── api/ # API routes
│
├── components/ # Shared components
│ ├── ui/ # UI components (buttons, inputs, etc.)
│ └── layout/ # Layout components (header, sidebar, etc.)
│
├── features/ # Feature-based modules
│ ├── feature/
│ │ ├── components/ # Feature-specific components
│ │ ├── actions/ # Server actions
│ │ ├── schemas/ # Form validation schemas
│ │ └── utils/ # Feature-specific utilities
│ │
├── lib/ # Core utilities and configurations
│ ├── auth/ # Auth configuration
│ ├── db/ # Database utilities
│ └── utils/ # Shared utilities
│
├── hooks/ # Custom hooks
│ └── use-debounce.ts
│
├── stores/ # Zustand stores
│ └── dashboard-store.ts
│
└── types/ # TypeScript types
└── index.ts
```

## Getting Started

> [!NOTE]  
> We are using **Next 15** with **React 19**, follow these steps:

Clone the repo:

```
git clone https://github.com/Kiranism/next-shadcn-dashboard-starter.git
```

- `pnpm install` ( we have legacy-peer-deps=true added in the .npmrc)
- Create a `.env.local` file by copying the example environment file:
  `cp env.example.txt .env.local`
- Add the required environment variables to the `.env.local` file.
- `pnpm run dev`

##### Environment Configuration Setup

To configure the environment for this project, refer to the `env.example.txt` file. This file contains the necessary environment variables required for authentication and error tracking.

You should now be able to access the application at http://localhost:3000.

> [!WARNING]
> After cloning or forking the repository, be cautious when pulling or syncing with the latest changes, as this may result in breaking conflicts.

Cheers! 🥂

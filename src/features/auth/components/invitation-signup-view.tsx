'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useUser, useSignUp } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';

interface InvitationData {
  id: string;
  code: string;
  role: 'student' | 'consultant';
  email?: string;
  expires_at: string;
  max_uses: number;
  current_uses: number;
  notes?: string;
}

interface SignUpFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
}

export default function InvitationSignUpView() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isSignedIn } = useUser();
  const { isLoaded, signUp, setActive } = useSignUp();

  const [invitationData, setInvitationData] = useState<InvitationData | null>(null);
  const [formData, setFormData] = useState<SignUpFormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [step, setStep] = useState<'signup' | 'verify' | 'complete'>('signup');
  const [verificationCode, setVerificationCode] = useState('');

  // Load invitation data from sessionStorage or URL
  useEffect(() => {
    const invitationCode = searchParams.get('invitation');
    const storedInvitation = sessionStorage.getItem('invitation_data');
    
    if (storedInvitation) {
      try {
        const invitation = JSON.parse(storedInvitation);
        setInvitationData(invitation);
        if (invitation.email) {
          setFormData(prev => ({ ...prev, email: invitation.email }));
        }
      } catch (error) {
        console.error('Error parsing stored invitation data:', error);
      }
    } else if (invitationCode) {
      // Redirect to invitation validation page
      router.push(`/auth/invitation?code=${invitationCode}`);
      return;
    } else {
      // No invitation data, redirect to invitation page
      router.push('/auth/invitation');
      return;
    }
  }, [searchParams, router]);

  // Redirect if already signed in
  useEffect(() => {
    if (isSignedIn) {
      router.push('/dashboard');
    }
  }, [isSignedIn, router]);

  const handleInputChange = (field: keyof SignUpFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isLoaded || !invitationData) return;

    setIsSubmitting(true);
    try {
      // Create Clerk account
      await signUp.create({
        emailAddress: formData.email,
        password: formData.password,
        firstName: formData.firstName,
        lastName: formData.lastName
      });

      // Send email verification
      await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });
      setStep('verify');
      toast.success('Verification code sent to your email');
    } catch (error: any) {
      console.error('Signup error:', error);
      toast.error(error.errors?.[0]?.message || 'Failed to create account');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isLoaded || !signUp) return;

    setIsSubmitting(true);
    try {
      // Verify email
      const completeSignUp = await signUp.attemptEmailAddressVerification({
        code: verificationCode
      });

      if (completeSignUp.status === 'complete') {
        await setActive({ session: completeSignUp.createdSessionId });
        
        // Create registration request
        const registrationResponse = await fetch('/api/registration-requests', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            email: formData.email,
            invitation_code: invitationData!.code,
            role_requested: invitationData!.role,
            profile_data: {
              first_name: formData.firstName,
              last_name: formData.lastName
            }
          })
        });

        const registrationResult = await registrationResponse.json();

        if (registrationResult.success) {
          // Clear stored invitation data
          sessionStorage.removeItem('invitation_data');
          setStep('complete');
          toast.success('Account created! Waiting for admin approval.');
        } else {
          toast.error(registrationResult.message || 'Failed to create registration request');
        }
      }
    } catch (error: any) {
      console.error('Verification error:', error);
      toast.error(error.errors?.[0]?.message || 'Failed to verify email');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!invitationData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Icons.spinner className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  const getRoleBadge = (role: string) => {
    const variants = {
      student: 'default',
      consultant: 'secondary'
    } as const;
    
    return (
      <Badge variant={variants[role as keyof typeof variants] || 'default'}>
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </Badge>
    );
  };

  if (step === 'complete') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <Card className="max-w-md w-full">
          <CardHeader className="text-center">
            <Icons.checkCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <CardTitle>Account Created Successfully!</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600">
              Your account has been created and is pending admin approval.
              You'll receive an email notification once your account is approved.
            </p>
            <Button onClick={() => router.push('/auth/sign-in')} className="w-full">
              Go to Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Create Your Account
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Complete your registration for Lighten Counsel
          </p>
        </div>

        {/* Invitation Info */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Invitation Role:</span>
              {getRoleBadge(invitationData.role)}
            </div>
            <div className="flex items-center justify-between mt-2">
              <span className="text-sm text-gray-600">Code:</span>
              <span className="font-mono text-sm">{invitationData.code}</span>
            </div>
          </CardContent>
        </Card>

        {step === 'signup' && (
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSignUp} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName">First Name</Label>
                    <Input
                      id="firstName"
                      type="text"
                      required
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      type="text"
                      required
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    required
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    disabled={!!invitationData.email}
                  />
                </div>

                <div>
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    required
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    minLength={8}
                  />
                </div>

                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full"
                >
                  {isSubmitting ? (
                    <>
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                      Creating Account...
                    </>
                  ) : (
                    'Create Account'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        )}

        {step === 'verify' && (
          <Card>
            <CardHeader>
              <CardTitle>Verify Your Email</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                We've sent a verification code to {formData.email}
              </p>
              <form onSubmit={handleVerification} className="space-y-4">
                <div>
                  <Label htmlFor="verificationCode">Verification Code</Label>
                  <Input
                    id="verificationCode"
                    type="text"
                    required
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    placeholder="Enter 6-digit code"
                    maxLength={6}
                    className="text-center font-mono text-lg tracking-wider"
                  />
                </div>

                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full"
                >
                  {isSubmitting ? (
                    <>
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    'Verify Email'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

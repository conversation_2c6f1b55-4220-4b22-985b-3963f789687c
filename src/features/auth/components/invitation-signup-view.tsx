'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useUser, useSignUp } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';

interface InvitationData {
  id: string;
  code: string;
  role: 'student' | 'consultant';
  email?: string;
  expires_at: string;
  max_uses: number;
  current_uses: number;
  notes?: string;
}



export default function InvitationSignUpView() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isSignedIn } = useUser();
  const { isLoaded, signUp, setActive } = useSignUp();

  const [invitationData, setInvitationData] = useState<InvitationData | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load invitation data from sessionStorage or URL
  useEffect(() => {
    const invitationCode = searchParams.get('invitation');
    const storedInvitation = sessionStorage.getItem('invitation_data');
    
    if (storedInvitation) {
      try {
        const invitation = JSON.parse(storedInvitation);
        setInvitationData(invitation);
        if (invitation.email) {
          setFormData(prev => ({ ...prev, email: invitation.email }));
        }
      } catch (error) {
        console.error('Error parsing stored invitation data:', error);
      }
    } else if (invitationCode) {
      // Redirect to invitation validation page
      router.push(`/auth/invitation?code=${invitationCode}`);
      return;
    } else {
      // No invitation data, redirect to invitation page
      router.push('/auth/invitation');
      return;
    }
  }, [searchParams, router]);

  // Redirect if already signed in
  useEffect(() => {
    if (isSignedIn) {
      router.push('/dashboard');
    }
  }, [isSignedIn, router]);



  const handleGoogleSignUp = async () => {
    if (!isLoaded || !invitationData) return;

    setIsSubmitting(true);
    try {
      // Store invitation data in sessionStorage for after OAuth redirect
      sessionStorage.setItem('invitation_data', JSON.stringify(invitationData));

      // Start Google OAuth flow
      await signUp.authenticateWithRedirect({
        strategy: 'oauth_google',
        redirectUrl: '/auth/sso-callback',
        redirectUrlComplete: '/auth/invitation-complete'
      });
    } catch (error: any) {
      console.error('Error during Google sign up:', error);
      toast.error(error.errors?.[0]?.message || 'Failed to sign up with Google');
      setIsSubmitting(false);
    }
  };



  if (!invitationData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Icons.spinner className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  const getRoleBadge = (role: string) => {
    const variants = {
      student: 'default',
      consultant: 'secondary'
    } as const;
    
    return (
      <Badge variant={variants[role as keyof typeof variants] || 'default'}>
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </Badge>
    );
  };

  if (step === 'complete') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <Card className="max-w-md w-full">
          <CardHeader className="text-center">
            <Icons.checkCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <CardTitle>Account Created Successfully!</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600">
              Your account has been created and is pending admin approval.
              You'll receive an email notification once your account is approved.
            </p>
            <Button onClick={() => router.push('/auth/sign-in')} className="w-full">
              Go to Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Create Your Account
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Complete your registration for Lighten Counsel
          </p>
        </div>

        {/* Invitation Info */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Invitation Role:</span>
              {getRoleBadge(invitationData.role)}
            </div>
            <div className="flex items-center justify-between mt-2">
              <span className="text-sm text-gray-600">Code:</span>
              <span className="font-mono text-sm">{invitationData.code}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Sign Up with Google</CardTitle>
            <p className="text-sm text-muted-foreground">
              To complete your invitation, you must sign up using your Google account.
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={handleGoogleSignUp}
              disabled={isSubmitting}
              className="w-full"
              size="lg"
            >
              {isSubmitting ? (
                <>
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  Redirecting to Google...
                </>
              ) : (
                <>
                  <Icons.google className="mr-2 h-4 w-4" />
                  Continue with Google
                </>
              )}
            </Button>

            <div className="text-center text-sm text-muted-foreground">
              <p>By continuing, you agree to our Terms of Service and Privacy Policy.</p>
            </div>
          </CardContent>
        </Card>


      </div>
    </div>
  );
}

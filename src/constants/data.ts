import { NavItem } from '@/types';

export type Product = {
  photo_url: string;
  name: string;
  description: string;
  created_at: string;
  price: number;
  id: number;
  category: string;
  updated_at: string;
};

// Role-based navigation items
export const studentNavItems: NavItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard',
    icon: 'dashboard',
    isActive: false,
    shortcut: ['d', 'd'],
    items: []
  },
  {
    title: 'Essays',
    url: '/dashboard/essays',
    icon: 'fileText',
    shortcut: ['e', 'e'],
    isActive: false,
    items: [
      {
        title: 'Personal Statement',
        url: '/dashboard/essays/personal-statement',
        icon: 'file'
      },
      {
        title: 'Supplemental Essays',
        url: '/dashboard/essays/supplemental',
        icon: 'files'
      }
    ]
  },
  {
    title: 'Academic Records',
    url: '/dashboard/academics',
    icon: 'graduationCap',
    shortcut: ['a', 'a'],
    isActive: false,
    items: [
      {
        title: 'GPA & Transcripts',
        url: '/dashboard/academics/transcripts-enhanced',
        icon: 'fileText'
      },
      {
        title: 'Standardized Tests',
        url: '/dashboard/academics/test-scores',
        icon: 'calculator'
      },
      {
        title: 'Mock Test Scores',
        url: '/dashboard/academics/mock-tests',
        icon: 'target'
      },
      {
        title: 'AP Performance',
        url: '/dashboard/academics/ap-courses',
        icon: 'award'
      }
    ]
  },
  {
    title: 'Activities',
    url: '/dashboard/activities',
    icon: 'trophy',
    shortcut: ['r', 'r'],
    isActive: false,
    items: []
  },
  {
    title: 'Schools',
    url: '/schools',
    icon: 'building',
    shortcut: ['s', 's'],
    isActive: false,
    items: []
  },
  {
    title: 'Documents',
    url: '/dashboard/documents',
    icon: 'folder',
    shortcut: ['f', 'f'],
    isActive: false,
    items: []
  }
];

export const consultantNavItems: NavItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard',
    icon: 'dashboard',
    isActive: false,
    shortcut: ['d', 'd'],
    items: []
  },
  {
    title: 'My Students',
    url: '/dashboard/students',
    icon: 'users',
    shortcut: ['s', 's'],
    isActive: false,
    items: []
  },
  {
    title: 'Documents',
    url: '/dashboard/documents',
    icon: 'folder',
    shortcut: ['f', 'f'],
    isActive: false,
    items: [
      {
        title: 'Review Queue',
        url: '/dashboard/documents/review',
        icon: 'eye'
      },
      {
        title: 'All Documents',
        url: '/dashboard/documents/all',
        icon: 'files'
      }
    ]
  },
  {
    title: 'Meetings',
    url: '/dashboard/meetings',
    icon: 'calendar',
    shortcut: ['m', 'm'],
    isActive: false,
    items: []
  },
  {
    title: 'Schools Database',
    url: '/schools',
    icon: 'building',
    shortcut: ['b', 'b'],
    isActive: false,
    items: []
  },
  {
    title: 'Account',
    url: '#',
    icon: 'user',
    isActive: false,
    items: [
      {
        title: 'Profile',
        url: '/dashboard/profile',
        icon: 'userPen',
        shortcut: ['p', 'p']
      }
    ]
  }
];

export const adminNavItems: NavItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard',
    icon: 'dashboard',
    isActive: false,
    shortcut: ['d', 'd'],
    items: []
  },
  {
    title: 'User Management',
    url: '/dashboard/users',
    icon: 'users',
    shortcut: ['u', 'u'],
    isActive: false,
    items: [
      {
        title: 'All Users',
        url: '/dashboard/users/all',
        icon: 'users'
      },
      {
        title: 'Students',
        url: '/dashboard/users/students',
        icon: 'graduationCap'
      },
      {
        title: 'Consultants',
        url: '/dashboard/users/consultants',
        icon: 'userCheck'
      },
      {
        title: 'Advisor Assignments',
        url: '/dashboard/admin/assignments',
        icon: 'userCog'
      },
      {
        title: 'Advisor Teams',
        url: '/dashboard/admin/advisor-teams',
        icon: 'users'
      }
    ]
  },
  {
    title: 'Invitations',
    url: '/dashboard/invitations',
    icon: 'mail',
    shortcut: ['i', 'i'],
    isActive: false,
    items: [
      {
        title: 'All Invitations',
        url: '/dashboard/invitations',
        icon: 'mail'
      },
      {
        title: 'Registration Requests',
        url: '/dashboard/invitations/requests',
        icon: 'userPlus'
      }
    ]
  },
  {
    title: 'Schools Management',
    url: '/schools',
    icon: 'building',
    shortcut: ['s', 's'],
    isActive: false,
    items: []
  },
  {
    title: 'System Analytics',
    url: '/dashboard/analytics',
    icon: 'barChart',
    shortcut: ['a', 'a'],
    isActive: false,
    items: []
  },
  {
    title: 'Documents',
    url: '/dashboard/documents',
    icon: 'folder',
    shortcut: ['f', 'f'],
    isActive: false,
    items: []
  },
  {
    title: 'Account',
    url: '#',
    icon: 'user',
    isActive: false,
    items: [
      {
        title: 'Profile',
        url: '/dashboard/profile',
        icon: 'userPen',
        shortcut: ['p', 'p']
      }
    ]
  }
];

// Legacy navigation for backward compatibility
export const navItems: NavItem[] = studentNavItems;

export interface SaleUser {
  id: number;
  name: string;
  email: string;
  amount: string;
  image: string;
  initials: string;
}

export const recentSalesData: SaleUser[] = [
  {
    id: 1,
    name: 'Olivia Martin',
    email: '<EMAIL>',
    amount: '+$1,999.00',
    image: 'https://api.slingacademy.com/public/sample-users/1.png',
    initials: 'OM'
  },
  {
    id: 2,
    name: 'Jackson Lee',
    email: '<EMAIL>',
    amount: '+$39.00',
    image: 'https://api.slingacademy.com/public/sample-users/2.png',
    initials: 'JL'
  },
  {
    id: 3,
    name: 'Isabella Nguyen',
    email: '<EMAIL>',
    amount: '+$299.00',
    image: 'https://api.slingacademy.com/public/sample-users/3.png',
    initials: 'IN'
  },
  {
    id: 4,
    name: 'William Kim',
    email: '<EMAIL>',
    amount: '+$99.00',
    image: 'https://api.slingacademy.com/public/sample-users/4.png',
    initials: 'WK'
  },
  {
    id: 5,
    name: 'Sofia Davis',
    email: '<EMAIL>',
    amount: '+$39.00',
    image: 'https://api.slingacademy.com/public/sample-users/5.png',
    initials: 'SD'
  }
];

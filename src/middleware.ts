import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { NextRequest } from 'next/server';

const isProtectedRoute = createRouteMatcher(['/dashboard(.*)']);
const isPublicApiRoute = createRouteMatcher([
  '/api/invitations/validate',
  '/api/registration-requests'
]);

export default clerkMiddleware(async (auth, req: NextRequest) => {
  // Skip auth for public API routes
  if (isPublicApiRoute(req)) {
    return;
  }

  if (isProtectedRoute(req)) await auth.protect();
});
export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)'
  ]
};

import { google } from 'googleapis';

interface InvitationEmailData {
  to: string;
  invitationCode: string;
  role: 'student' | 'consultant';
  expiresAt: string;
  notes?: string;
  inviterName?: string;
}

export class EmailService {
  private static async getGmailClient() {
    // Create JWT client for Gmail API with domain-wide delegation
    const auth = new google.auth.JWT({
      email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL!,
      key: process.env.GOOGLE_PRIVATE_KEY!.replace(/\\n/g, '\n'),
      scopes: ['https://www.googleapis.com/auth/gmail.send'],
      subject: process.env.GOOGLE_ADMIN_EMAIL! // Impersonate the admin user
    });

    await auth.authorize();
    return google.gmail({ version: 'v1', auth });
  }

  private static createInvitationEmailContent(data: InvitationEmailData): string {
    const { invitationCode, role, expiresAt, notes, inviterName } = data;
    const expirationDate = new Date(expiresAt).toLocaleDateString();
    const signupUrl = `${process.env.NEXT_PUBLIC_APP_URL}/auth/invitation?code=${invitationCode}`;

    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invitation to Lighten Counsel</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
        .invitation-code { background: #1e40af; color: white; padding: 15px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 2px; border-radius: 6px; margin: 20px 0; }
        .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 20px 0; }
        .info-box { background: white; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #2563eb; }
        .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎓 Welcome to Lighten Counsel</h1>
        <p>You've been invited to join our college application platform</p>
    </div>
    
    <div class="content">
        <h2>Hello!</h2>
        
        <p>You've been invited to join <strong>Lighten Counsel</strong> as a <strong>${role}</strong>. ${inviterName ? `This invitation was sent by ${inviterName}.` : ''}</p>
        
        <div class="invitation-code">
            ${invitationCode}
        </div>
        
        <p>Use this invitation code to complete your registration:</p>
        
        <div style="text-align: center;">
            <a href="${signupUrl}" class="button">Complete Registration</a>
        </div>
        
        <div class="info-box">
            <h3>📋 Invitation Details</h3>
            <ul>
                <li><strong>Role:</strong> ${role.charAt(0).toUpperCase() + role.slice(1)}</li>
                <li><strong>Invitation Code:</strong> ${invitationCode}</li>
                <li><strong>Expires:</strong> ${expirationDate}</li>
            </ul>
            ${notes ? `<p><strong>Additional Notes:</strong> ${notes}</p>` : ''}
        </div>
        
        <h3>🚀 What's Next?</h3>
        <ol>
            <li>Click the "Complete Registration" button above</li>
            <li>Your invitation code will be automatically validated</li>
            <li>Create your account with email and password</li>
            <li>Verify your email address</li>
            <li>Wait for admin approval</li>
            <li>Start your college application journey!</li>
        </ol>
        
        <div class="info-box">
            <h4>⚠️ Important Notes</h4>
            <ul>
                <li>This invitation expires on <strong>${expirationDate}</strong></li>
                <li>The invitation code is case-sensitive</li>
                <li>Keep this email for your records</li>
                <li>Contact support if you have any issues</li>
            </ul>
        </div>
    </div>
    
    <div class="footer">
        <p>This invitation was sent by Lighten Counsel</p>
        <p>If you didn't expect this invitation, please ignore this email.</p>
    </div>
</body>
</html>
    `.trim();
  }

  static async sendInvitationEmail(data: InvitationEmailData): Promise<void> {
    try {
      const gmail = await this.getGmailClient();
      const htmlContent = this.createInvitationEmailContent(data);
      
      // Create the email message
      const subject = `Invitation to Lighten Counsel - ${data.role.charAt(0).toUpperCase() + data.role.slice(1)} Access`;
      const message = [
        `From: ${process.env.GOOGLE_ADMIN_EMAIL}`,
        `To: ${data.to}`,
        `Subject: ${subject}`,
        'MIME-Version: 1.0',
        'Content-Type: text/html; charset=utf-8',
        '',
        htmlContent
      ].join('\r\n');

      // Encode the message in base64
      const encodedMessage = Buffer.from(message)
        .toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');

      // Send the email
      await gmail.users.messages.send({
        userId: 'me',
        requestBody: {
          raw: encodedMessage
        }
      });

      console.log(`Invitation email sent successfully to ${data.to}`);
    } catch (error) {
      console.error('Failed to send invitation email:', error);
      throw new Error(`Failed to send invitation email: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  static async sendBulkInvitationEmails(invitations: InvitationEmailData[]): Promise<{
    successful: string[];
    failed: { email: string; error: string }[];
  }> {
    const successful: string[] = [];
    const failed: { email: string; error: string }[] = [];

    for (const invitation of invitations) {
      try {
        await this.sendInvitationEmail(invitation);
        successful.push(invitation.to);
      } catch (error) {
        failed.push({
          email: invitation.to,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return { successful, failed };
  }
}

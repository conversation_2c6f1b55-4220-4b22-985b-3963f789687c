export { SchoolSearch } from './school-search';
export { SchoolSelectorDialog } from './school-selector-dialog';

// Types
export interface School {
  id: string;
  scorecard_id: number;
  name: string;
  city: string;
  state: string;
  zip: string;
  school_url: string;
  ownership: number;
  admission_rate: number;
  tuition_in_state: number;
  tuition_out_of_state: number;
  student_size: number;
  offers_bachelors: boolean;
  ownership_label: string;
  tuition_display: string;
  admission_rate_percent: number;
}

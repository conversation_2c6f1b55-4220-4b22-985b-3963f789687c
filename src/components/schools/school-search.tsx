'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Search, Filter, MapPin, Users, DollarSign, TrendingUp } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useDebounce } from '@/hooks/use-debounce';

interface School {
  id: string;
  scorecard_id: number;
  name: string;
  city: string;
  state: string;
  zip: string;
  school_url: string;
  ownership: number;
  admission_rate: number;
  tuition_in_state: number;
  tuition_out_of_state: number;
  student_size: number;
  offers_bachelors: boolean;
  ownership_label: string;
  tuition_display: string;
  admission_rate_percent: number;
}

interface SchoolSearchResponse {
  success: boolean;
  data: {
    schools: School[];
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

interface SchoolSearchProps {
  onSchoolSelect?: (school: School) => void;
  selectedSchools?: School[];
  multiSelect?: boolean;
  placeholder?: string;
  className?: string;
  enableNavigation?: boolean;
}

const US_STATES = [
  'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA',
  'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD',
  'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ',
  'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC',
  'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
];

export function SchoolSearch({
  onSchoolSelect,
  selectedSchools = [],
  multiSelect = false,
  placeholder = "Search for schools...",
  className = "",
  enableNavigation = false
}: SchoolSearchProps) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedState, setSelectedState] = useState<string>('');
  const [selectedOwnership, setSelectedOwnership] = useState<string>('');
  const [bachelorsOnly, setBachelorsOnly] = useState(false);
  const [schools, setSchools] = useState<School[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [offset, setOffset] = useState(0);
  const [showFilters, setShowFilters] = useState(false);

  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const searchSchools = useCallback(async (resetResults = true) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        limit: '20',
        offset: resetResults ? '0' : offset.toString()
      });

      if (debouncedSearchQuery.trim()) {
        params.append('q', debouncedSearchQuery.trim());
      }
      if (selectedState) {
        params.append('state', selectedState);
      }
      if (selectedOwnership) {
        params.append('ownership', selectedOwnership);
      }
      if (bachelorsOnly) {
        params.append('bachelors', 'true');
      }

      const response = await fetch(`/api/schools/search?${params.toString()}`);
      const data: SchoolSearchResponse = await response.json();

      if (data.success) {
        if (resetResults) {
          setSchools(data.data.schools);
          setOffset(20);
        } else {
          setSchools(prev => [...prev, ...data.data.schools]);
          setOffset(prev => prev + 20);
        }
        setHasMore(data.data.hasMore);
      }
    } catch (error) {
      console.error('Error searching schools:', error);
    } finally {
      setLoading(false);
    }
  }, [debouncedSearchQuery, selectedState, selectedOwnership, bachelorsOnly, offset]);

  useEffect(() => {
    searchSchools(true);
  }, [debouncedSearchQuery, selectedState, selectedOwnership, bachelorsOnly]);

  const handleSchoolSelect = (school: School) => {
    if (enableNavigation) {
      router.push(`/schools/${school.id}`);
    } else if (onSchoolSelect) {
      onSchoolSelect(school);
    }
  };

  const isSchoolSelected = (school: School) => {
    return selectedSchools.some(s => s.id === school.id);
  };

  const loadMore = () => {
    if (!loading && hasMore) {
      searchSchools(false);
    }
  };

  const clearFilters = () => {
    setSelectedState('');
    setSelectedOwnership('');
    setBachelorsOnly(false);
    setSearchQuery('');
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder={placeholder}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-12"
        />
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
          className="absolute right-1 top-1/2 transform -translate-y-1/2"
        >
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Filters</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">State</label>
                <Select value={selectedState} onValueChange={setSelectedState}>
                  <SelectTrigger>
                    <SelectValue placeholder="All states" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All states</SelectItem>
                    {US_STATES.map(state => (
                      <SelectItem key={state} value={state}>{state}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-2 block">Ownership</label>
                <Select value={selectedOwnership} onValueChange={setSelectedOwnership}>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All types</SelectItem>
                    <SelectItem value="1">Public</SelectItem>
                    <SelectItem value="2">Private Nonprofit</SelectItem>
                    <SelectItem value="3">Private For-Profit</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2 pt-6">
                <input
                  type="checkbox"
                  id="bachelors-only"
                  checked={bachelorsOnly}
                  onChange={(e) => setBachelorsOnly(e.target.checked)}
                  className="rounded"
                />
                <label htmlFor="bachelors-only" className="text-sm font-medium">
                  Bachelor's programs only
                </label>
              </div>
            </div>
            
            <Button variant="outline" size="sm" onClick={clearFilters}>
              Clear Filters
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      <div className="space-y-3">
        {loading && schools.length === 0 ? (
          // Loading skeletons
          Array.from({ length: 5 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2 mb-2" />
                <div className="flex space-x-2">
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-6 w-20" />
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          schools.map((school) => (
            <SchoolCard
              key={school.id}
              school={school}
              onSelect={() => handleSchoolSelect(school)}
              isSelected={isSchoolSelected(school)}
              multiSelect={multiSelect}
              enableNavigation={enableNavigation}
            />
          ))
        )}

        {/* Load More Button */}
        {hasMore && (
          <div className="text-center pt-4">
            <Button
              variant="outline"
              onClick={loadMore}
              disabled={loading}
            >
              {loading ? 'Loading...' : 'Load More Schools'}
            </Button>
          </div>
        )}

        {/* No Results */}
        {!loading && schools.length === 0 && (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-muted-foreground">No schools found matching your criteria.</p>
              <Button variant="outline" size="sm" onClick={clearFilters} className="mt-2">
                Clear Filters
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

interface SchoolCardProps {
  school: School;
  onSelect: () => void;
  isSelected: boolean;
  multiSelect: boolean;
  enableNavigation?: boolean;
}

function SchoolCard({ school, onSelect, isSelected, multiSelect, enableNavigation }: SchoolCardProps) {
  return (
    <Card 
      className={`cursor-pointer transition-all hover:shadow-md ${
        isSelected ? 'ring-2 ring-primary' : ''
      }`}
      onClick={onSelect}
    >
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-semibold text-lg leading-tight">{school.name}</h3>
          {isSelected && multiSelect && (
            <Badge variant="default">Selected</Badge>
          )}
        </div>
        
        <div className="flex items-center text-sm text-muted-foreground mb-3">
          <MapPin className="h-4 w-4 mr-1" />
          {school.city}, {school.state}
        </div>

        <div className="flex flex-wrap gap-2 mb-3">
          <Badge variant="secondary">{school.ownership_label}</Badge>
          {school.admission_rate_percent && (
            <Badge variant="outline" className="flex items-center gap-1">
              <TrendingUp className="h-3 w-3" />
              {school.admission_rate_percent}% admission rate
            </Badge>
          )}
          {school.student_size && (
            <Badge variant="outline" className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              {school.student_size.toLocaleString()} students
            </Badge>
          )}
        </div>

        {school.tuition_display !== 'N/A' && (
          <div className="flex items-center text-sm">
            <DollarSign className="h-4 w-4 mr-1 text-muted-foreground" />
            <span className="text-muted-foreground">Tuition: </span>
            <span className="font-medium ml-1">{school.tuition_display}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

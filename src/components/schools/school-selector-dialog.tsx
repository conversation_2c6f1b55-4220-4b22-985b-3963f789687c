'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>T<PERSON>le, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { X, Plus, School } from 'lucide-react';
import { SchoolSearch } from './school-search';

interface School {
  id: string;
  scorecard_id: number;
  name: string;
  city: string;
  state: string;
  zip: string;
  school_url: string;
  ownership: number;
  admission_rate: number;
  tuition_in_state: number;
  tuition_out_of_state: number;
  student_size: number;
  offers_bachelors: boolean;
  ownership_label: string;
  tuition_display: string;
  admission_rate_percent: number;
}

interface SchoolSelectorDialogProps {
  selectedSchools: School[];
  onSchoolsChange: (schools: School[]) => void;
  multiSelect?: boolean;
  maxSelections?: number;
  triggerText?: string;
  title?: string;
  description?: string;
}

export function SchoolSelectorDialog({
  selectedSchools,
  onSchoolsChange,
  multiSelect = true,
  maxSelections,
  triggerText = "Add Schools",
  title = "Select Schools",
  description = "Search and select schools from our database"
}: SchoolSelectorDialogProps) {
  const [open, setOpen] = useState(false);
  const [tempSelectedSchools, setTempSelectedSchools] = useState<School[]>(selectedSchools);

  const handleSchoolSelect = (school: School) => {
    if (!multiSelect) {
      setTempSelectedSchools([school]);
      return;
    }

    const isAlreadySelected = tempSelectedSchools.some(s => s.id === school.id);
    
    if (isAlreadySelected) {
      // Remove school
      setTempSelectedSchools(prev => prev.filter(s => s.id !== school.id));
    } else {
      // Add school (check max selections)
      if (maxSelections && tempSelectedSchools.length >= maxSelections) {
        return; // Don't add if at max
      }
      setTempSelectedSchools(prev => [...prev, school]);
    }
  };

  const handleRemoveSchool = (schoolId: string) => {
    setTempSelectedSchools(prev => prev.filter(s => s.id !== schoolId));
  };

  const handleSave = () => {
    onSchoolsChange(tempSelectedSchools);
    setOpen(false);
  };

  const handleCancel = () => {
    setTempSelectedSchools(selectedSchools);
    setOpen(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      handleCancel();
    }
    setOpen(newOpen);
  };

  return (
    <div className="space-y-4">
      {/* Selected Schools Display */}
      {selectedSchools.length > 0 && (
        <div className="space-y-2">
          <label className="text-sm font-medium">Selected Schools:</label>
          <div className="flex flex-wrap gap-2">
            {selectedSchools.map((school) => (
              <Badge
                key={school.id}
                variant="secondary"
                className="flex items-center gap-2 px-3 py-1"
              >
                <School className="h-3 w-3" />
                <span>{school.name}</span>
                <button
                  onClick={() => onSchoolsChange(selectedSchools.filter(s => s.id !== school.id))}
                  className="ml-1 hover:bg-destructive hover:text-destructive-foreground rounded-full p-0.5"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Dialog Trigger */}
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            {triggerText}
            {maxSelections && (
              <Badge variant="secondary" className="ml-2">
                {selectedSchools.length}/{maxSelections}
              </Badge>
            )}
          </Button>
        </DialogTrigger>
        
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
          </DialogHeader>

          {/* Selected Schools in Dialog */}
          {tempSelectedSchools.length > 0 && (
            <div className="border-b pb-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">
                  Selected ({tempSelectedSchools.length}
                  {maxSelections && `/${maxSelections}`})
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setTempSelectedSchools([])}
                >
                  Clear All
                </Button>
              </div>
              <div className="flex flex-wrap gap-2 max-h-20 overflow-y-auto">
                {tempSelectedSchools.map((school) => (
                  <Badge
                    key={school.id}
                    variant="default"
                    className="flex items-center gap-2 px-2 py-1"
                  >
                    <span className="text-xs">{school.name}</span>
                    <button
                      onClick={() => handleRemoveSchool(school.id)}
                      className="hover:bg-primary-foreground rounded-full p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* School Search */}
          <div className="flex-1 overflow-hidden">
            <SchoolSearch
              onSchoolSelect={handleSchoolSelect}
              selectedSchools={tempSelectedSchools}
              multiSelect={multiSelect}
              placeholder="Search for schools to add..."
              className="h-full overflow-y-auto"
            />
          </div>

          {/* Dialog Actions */}
          <div className="flex justify-between items-center pt-4 border-t">
            <div className="text-sm text-muted-foreground">
              {maxSelections && (
                <>
                  {tempSelectedSchools.length} of {maxSelections} schools selected
                  {tempSelectedSchools.length >= maxSelections && (
                    <span className="text-amber-600 ml-2">Maximum reached</span>
                  )}
                </>
              )}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button onClick={handleSave}>
                Save Selection
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

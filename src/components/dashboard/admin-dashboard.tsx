'use client';

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Icons } from '@/components/icons';
import Link from 'next/link';
import { useEffect } from 'react';
import { useApiGet } from '@/lib/api-client';
import type { User } from '@/types/application';

interface AdminDashboardProps {
  user: User;
}

interface AdminDashboardStats {
  user: {
    totalUsers: number;
    totalStudents: number;
    totalConsultants: number;
    activeUsers: number;
    newUsersThisMonth: number;
  };
  documents: {
    totalDocuments: number;
    documentsWithGoogleDocs: number;
    googleDocsIntegrationPercentage: number;
    documentsByType: Record<string, number>;
    completionStats: {
      draft: number;
      in_review: number;
      completed: number;
    };
    recentActivityCount: number;
  };
  applications: {
    totalApplications: number;
    activeApplications: number;
    submittedApplications: number;
    averageProgress: number;
  };
  activity: {
    totalActivities: number;
    totalHours: number;
    categoriesCount: number;
    leadershipRoles: number;
  };
}

export default function AdminDashboard({ user }: AdminDashboardProps) {
  const {
    data: stats,
    loading,
    error,
    fetch: fetchStats
  } = useApiGet<AdminDashboardStats>('/api/stats/dashboard', { role: 'admin' });

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  // Use real data if available, fallback to defaults
  const totalUsers = stats?.user.totalUsers || 0;
  const totalStudents = stats?.user.totalStudents || 0;
  const totalConsultants = stats?.user.totalConsultants || 0;
  const totalDocuments = stats?.documents.totalDocuments || 0;
  const activeApplications = stats?.applications.activeApplications || 0;

  return (
    <div className='space-y-6'>
      {/* Welcome Section */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold'>Admin Dashboard</h1>
          <p className='text-muted-foreground'>
            System overview, user management, and security monitoring
          </p>
        </div>
        <div className='flex gap-2'>
          <Button asChild variant='outline'>
            <Link href='/dashboard/admin/settings'>
              <Icons.settings className='mr-2 h-4 w-4' />
              System Settings
            </Link>
          </Button>
          <Button asChild>
            <Link href='/dashboard/users/all'>
              <Icons.users className='mr-2 h-4 w-4' />
              Manage Users
            </Link>
          </Button>
        </div>
      </div>

      {/* System Stats */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-5'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Users</CardTitle>
            <Icons.users className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className='flex items-center justify-center py-2'>
                <Icons.spinner className='h-4 w-4 animate-spin' />
              </div>
            ) : (
              <>
                <div className='text-2xl font-bold'>{totalUsers}</div>
                <p className='text-muted-foreground text-xs'>
                  +{stats?.user.newUsersThisMonth || 0} from last month
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Students</CardTitle>
            <Icons.graduationCap className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{totalStudents}</div>
            <p className='text-muted-foreground text-xs'>
              {totalUsers > 0 ? Math.round((totalStudents / totalUsers) * 100) : 0}% of total users
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Consultants</CardTitle>
            <Icons.userCheck className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{totalConsultants}</div>
            <p className='text-muted-foreground text-xs'>
              Avg. {totalConsultants > 0 ? Math.round(totalStudents / totalConsultants) : 0} students each
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Documents</CardTitle>
            <Icons.fileText className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className='flex items-center justify-center py-2'>
                <Icons.spinner className='h-4 w-4 animate-spin' />
              </div>
            ) : (
              <>
                <div className='text-2xl font-bold'>{totalDocuments}</div>
                <p className='text-muted-foreground text-xs'>
                  {stats?.documents.googleDocsIntegrationPercentage || 0}% with
                  Google Docs
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Applications</CardTitle>
            <Icons.fileText className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className='flex items-center justify-center py-2'>
                <Icons.spinner className='h-4 w-4 animate-spin' />
              </div>
            ) : (
              <>
                <div className='text-2xl font-bold'>{activeApplications}</div>
                <p className='text-muted-foreground text-xs'>
                  Active this cycle
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Enhanced System Analytics */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.trendingUp className='h-5 w-5 text-blue-500' />
              User Activity Rate
            </CardTitle>
            <CardDescription>Daily active users</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>89%</div>
            <p className='text-muted-foreground text-xs'>+5% from last week</p>
            <div className='mt-2 h-2 w-full rounded-full bg-gray-200'>
              <div className='h-2 w-[89%] rounded-full bg-blue-500'></div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.fileText className='h-5 w-5 text-green-500' />
              Document Creation Rate
            </CardTitle>
            <CardDescription>Documents per day</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>127</div>
            <p className='text-muted-foreground text-xs'>+23 from yesterday</p>
            <div className='mt-2 h-2 w-full rounded-full bg-gray-200'>
              <div className='h-2 w-[75%] rounded-full bg-green-500'></div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.shield className='h-5 w-5 text-purple-500' />
              Security Score
            </CardTitle>
            <CardDescription>System security rating</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>95%</div>
            <p className='text-muted-foreground text-xs'>Excellent security</p>
            <div className='mt-2 h-2 w-full rounded-full bg-gray-200'>
              <div className='h-2 w-[95%] rounded-full bg-purple-500'></div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.database className='h-5 w-5 text-orange-500' />
              System Performance
            </CardTitle>
            <CardDescription>Response time & uptime</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>99.9%</div>
            <p className='text-muted-foreground text-xs'>Avg 120ms response</p>
            <div className='mt-2 h-2 w-full rounded-full bg-gray-200'>
              <div className='h-2 w-[99%] rounded-full bg-orange-500'></div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* User Management & Security Monitoring */}
      <div className='grid gap-4 md:grid-cols-2'>
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.users className='h-5 w-5' />
              User Management & Role Assignment
            </CardTitle>
            <CardDescription>
              Comprehensive user administration and role controls
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-3'>
            <div className='rounded-lg border p-3'>
              <div className='mb-2 flex items-center justify-between'>
                <p className='font-medium'>Pending Role Assignments</p>
                <Badge variant='secondary'>5</Badge>
              </div>
              <div className='space-y-2 text-sm'>
                <div className='flex justify-between'>
                  <span>New consultants awaiting approval</span>
                  <span className='text-muted-foreground'>3</span>
                </div>
                <div className='flex justify-between'>
                  <span>Student role upgrades</span>
                  <span className='text-muted-foreground'>2</span>
                </div>
              </div>
            </div>

            <div className='rounded-lg border p-3'>
              <div className='mb-2 flex items-center justify-between'>
                <p className='font-medium'>Recent Role Changes</p>
                <Badge variant='outline'>Today</Badge>
              </div>
              <div className='space-y-1 text-sm'>
                <div className='flex justify-between'>
                  <span>Dr. Sarah Wilson → Consultant</span>
                  <span className='text-muted-foreground'>2h ago</span>
                </div>
                <div className='flex justify-between'>
                  <span>Mike Johnson → Admin</span>
                  <span className='text-muted-foreground'>5h ago</span>
                </div>
              </div>
            </div>

            <div className='flex gap-2'>
              <Button size='sm' className='flex-1'>
                <Icons.userCheck className='mr-2 h-4 w-4' />
                Review Roles
              </Button>
              <Button size='sm' variant='outline' className='flex-1' asChild>
                <Link href='/dashboard/invitations'>
                  <Icons.plus className='mr-2 h-4 w-4' />
                  Add User
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.shield className='h-5 w-5' />
              Security Monitoring & Audit Logs
            </CardTitle>
            <CardDescription>
              Real-time security alerts and system audit trail
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-3'>
            <div className='rounded-lg border border-green-200 bg-green-50 p-3'>
              <div className='mb-1 flex items-center gap-2'>
                <Icons.checkCircle className='h-4 w-4 text-green-600' />
                <p className='text-sm font-medium text-green-900'>
                  System Secure
                </p>
              </div>
              <p className='text-xs text-green-700'>
                No security threats detected
              </p>
            </div>

            <div className='space-y-2'>
              <div className='flex items-center justify-between rounded border p-2'>
                <div className='flex items-center gap-2'>
                  <Icons.eye className='h-4 w-4 text-blue-500' />
                  <span className='text-sm'>Failed login attempts</span>
                </div>
                <Badge variant='outline' className='text-xs'>
                  3 today
                </Badge>
              </div>

              <div className='flex items-center justify-between rounded border p-2'>
                <div className='flex items-center gap-2'>
                  <Icons.key className='h-4 w-4 text-purple-500' />
                  <span className='text-sm'>Password resets</span>
                </div>
                <Badge variant='outline' className='text-xs'>
                  7 today
                </Badge>
              </div>

              <div className='flex items-center justify-between rounded border p-2'>
                <div className='flex items-center gap-2'>
                  <Icons.userX className='h-4 w-4 text-orange-500' />
                  <span className='text-sm'>Account suspensions</span>
                </div>
                <Badge variant='outline' className='text-xs'>
                  0 today
                </Badge>
              </div>
            </div>

            <div className='flex gap-2'>
              <Button size='sm' className='flex-1'>
                <Icons.fileText className='mr-2 h-4 w-4' />
                View Audit Log
              </Button>
              <Button size='sm' variant='outline' className='flex-1'>
                <Icons.download className='mr-2 h-4 w-4' />
                Export Report
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Management Tools & System Activity */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-7'>
        <Card className='md:col-span-4'>
          <CardHeader>
            <CardTitle>Recent System Activity</CardTitle>
            <CardDescription>
              Latest user actions and system events
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='flex items-center space-x-4'>
              <div className='flex h-8 w-8 items-center justify-center rounded-full bg-green-100'>
                <Icons.userCheck className='h-4 w-4 text-green-600' />
              </div>
              <div className='flex-1 space-y-1'>
                <p className='text-sm font-medium'>New consultant registered</p>
                <p className='text-muted-foreground text-xs'>
                  Dr. Sarah Wilson joined the platform
                </p>
              </div>
              <div className='text-muted-foreground text-xs'>2 hours ago</div>
            </div>

            <div className='flex items-center space-x-4'>
              <div className='flex h-8 w-8 items-center justify-center rounded-full bg-blue-100'>
                <Icons.graduationCap className='h-4 w-4 text-blue-600' />
              </div>
              <div className='flex-1 space-y-1'>
                <p className='text-sm font-medium'>
                  Bulk student import completed
                </p>
                <p className='text-muted-foreground text-xs'>
                  25 new students added from Lincoln High
                </p>
              </div>
              <div className='text-muted-foreground text-xs'>5 hours ago</div>
            </div>

            <div className='flex items-center space-x-4'>
              <div className='flex h-8 w-8 items-center justify-center rounded-full bg-purple-100'>
                <Icons.building className='h-4 w-4 text-purple-600' />
              </div>
              <div className='flex-1 space-y-1'>
                <p className='text-sm font-medium'>School database updated</p>
                <p className='text-muted-foreground text-xs'>
                  2025 application requirements synced
                </p>
              </div>
              <div className='text-muted-foreground text-xs'>1 day ago</div>
            </div>

            <div className='flex items-center space-x-4'>
              <div className='flex h-8 w-8 items-center justify-center rounded-full bg-orange-100'>
                <Icons.barChart className='h-4 w-4 text-orange-600' />
              </div>
              <div className='flex-1 space-y-1'>
                <p className='text-sm font-medium'>Monthly report generated</p>
                <p className='text-muted-foreground text-xs'>
                  October analytics report available
                </p>
              </div>
              <div className='text-muted-foreground text-xs'>2 days ago</div>
            </div>
          </CardContent>
        </Card>

        <Card className='md:col-span-3'>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.settings className='h-5 w-5' />
              Administrative Controls & System Settings
            </CardTitle>
            <CardDescription>
              System management and configuration
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-3'>
            <div className='rounded-lg border border-blue-200 bg-blue-50 p-3'>
              <p className='mb-2 text-sm font-medium text-blue-900'>
                Quick Actions
              </p>
              <div className='grid grid-cols-2 gap-2'>
                <Button size='sm' asChild>
                  <Link href='/dashboard/users/all'>
                    <Icons.users className='mr-2 h-4 w-4' />
                    Users
                  </Link>
                </Button>
                <Button size='sm' variant='outline' asChild>
                  <Link href='/dashboard/admin/backup'>
                    <Icons.database className='mr-2 h-4 w-4' />
                    Backup
                  </Link>
                </Button>
              </div>
            </div>

            <Button asChild className='w-full justify-start'>
              <Link href='/dashboard/admin/settings'>
                <Icons.settings className='mr-2 h-4 w-4' />
                System Configuration
              </Link>
            </Button>

            <Button asChild variant='outline' className='w-full justify-start'>
              <Link href='/dashboard/schools'>
                <Icons.building className='mr-2 h-4 w-4' />
                School Database Management
              </Link>
            </Button>

            <Button asChild variant='outline' className='w-full justify-start'>
              <Link href='/dashboard/analytics/advanced'>
                <Icons.barChart className='mr-2 h-4 w-4' />
                Advanced Analytics
              </Link>
            </Button>

            <Button asChild variant='outline' className='w-full justify-start'>
              <Link href='/dashboard/admin/maintenance'>
                <Icons.wrench className='mr-2 h-4 w-4' />
                System Maintenance
              </Link>
            </Button>

            <Button asChild variant='outline' className='w-full justify-start'>
              <Link href='/dashboard/documents/admin'>
                <Icons.folder className='mr-2 h-4 w-4' />
                Document Administration
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* User Distribution & Alerts */}
      <div className='grid gap-4 md:grid-cols-2'>
        <Card>
          <CardHeader>
            <CardTitle>User Distribution</CardTitle>
            <CardDescription>Breakdown by role and status</CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center space-x-2'>
                <Icons.graduationCap className='h-4 w-4 text-blue-500' />
                <span className='text-sm'>Students</span>
              </div>
              <div className='flex items-center space-x-2'>
                <div className='h-2 w-20 rounded-full bg-gray-200'>
                  <div
                    className='h-2 rounded-full bg-blue-500'
                    style={{ width: '73%' }}
                  />
                </div>
                <span className='text-sm font-medium'>{totalStudents}</span>
              </div>
            </div>

            <div className='flex items-center justify-between'>
              <div className='flex items-center space-x-2'>
                <Icons.userCheck className='h-4 w-4 text-green-500' />
                <span className='text-sm'>Consultants</span>
              </div>
              <div className='flex items-center space-x-2'>
                <div className='h-2 w-20 rounded-full bg-gray-200'>
                  <div
                    className='h-2 rounded-full bg-green-500'
                    style={{ width: '6%' }}
                  />
                </div>
                <span className='text-sm font-medium'>{totalConsultants}</span>
              </div>
            </div>

            <div className='flex items-center justify-between'>
              <div className='flex items-center space-x-2'>
                <Icons.user className='h-4 w-4 text-purple-500' />
                <span className='text-sm'>Admins</span>
              </div>
              <div className='flex items-center space-x-2'>
                <div className='h-2 w-20 rounded-full bg-gray-200'>
                  <div
                    className='h-2 rounded-full bg-purple-500'
                    style={{ width: '2%' }}
                  />
                </div>
                <span className='text-sm font-medium'>
                  {totalUsers - totalStudents - totalConsultants}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Alerts</CardTitle>
            <CardDescription>Issues requiring attention</CardDescription>
          </CardHeader>
          <CardContent className='space-y-3'>
            <div className='flex items-center space-x-3 rounded-lg border border-yellow-200 bg-yellow-50 p-3'>
              <Icons.warning className='h-5 w-5 text-yellow-600' />
              <div className='flex-1'>
                <p className='text-sm font-medium'>High consultant workload</p>
                <p className='text-muted-foreground text-xs'>
                  3 consultants have 15+ students
                </p>
              </div>
              <Badge variant='secondary'>Warning</Badge>
            </div>

            <div className='flex items-center space-x-3 rounded-lg border border-blue-200 bg-blue-50 p-3'>
              <Icons.calendar className='h-5 w-5 text-blue-600' />
              <div className='flex-1'>
                <p className='text-sm font-medium'>
                  Application deadline approaching
                </p>
                <p className='text-muted-foreground text-xs'>
                  Early Action deadlines in 5 days
                </p>
              </div>
              <Badge variant='secondary'>Info</Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { SchoolSearch, SchoolSelectorDialog, School } from '@/components/schools';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { School as SchoolIcon, Target, Heart, BookOpen } from 'lucide-react';

export default function SchoolsPage() {
  const [targetSchools, setTargetSchools] = useState<School[]>([]);
  const [safetySchools, setSafetySchools] = useState<School[]>([]);
  const [reachSchools, setReachSchools] = useState<School[]>([]);
  const [favoriteSchools, setFavoriteSchools] = useState<School[]>([]);

  const allSelectedSchools = [
    ...targetSchools,
    ...safetySchools,
    ...reachSchools,
    ...favoriteSchools
  ];

  const getSchoolStats = () => {
    const totalSchools = allSelectedSchools.length;
    const publicSchools = allSelectedSchools.filter(s => s.ownership === 1).length;
    const privateSchools = allSelectedSchools.filter(s => s.ownership !== 1).length;
    const avgAdmissionRate = allSelectedSchools
      .filter(s => s.admission_rate_percent)
      .reduce((sum, s) => sum + s.admission_rate_percent, 0) / 
      allSelectedSchools.filter(s => s.admission_rate_percent).length;

    return {
      totalSchools,
      publicSchools,
      privateSchools,
      avgAdmissionRate: isNaN(avgAdmissionRate) ? 0 : Math.round(avgAdmissionRate)
    };
  };

  const stats = getSchoolStats();

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">School Selection</h2>
          <p className="text-muted-foreground">
            Search and organize your target schools from our database of 6,300+ institutions
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Schools</CardTitle>
            <SchoolIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalSchools}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Public Schools</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.publicSchools}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Private Schools</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.privateSchools}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Admission Rate</CardTitle>
            <Heart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.avgAdmissionRate}%</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="search" className="space-y-4">
        <TabsList>
          <TabsTrigger value="search">Search Schools</TabsTrigger>
          <TabsTrigger value="organize">Organize Schools</TabsTrigger>
          <TabsTrigger value="overview">Overview</TabsTrigger>
        </TabsList>

        <TabsContent value="search" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>School Search</CardTitle>
              <p className="text-sm text-muted-foreground">
                Search through our database of 6,300+ schools. Click on any school to view details.
              </p>
            </CardHeader>
            <CardContent>
              <SchoolSearch
                enableNavigation={true}
                placeholder="Search for colleges and universities..."
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="organize" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-blue-500" />
                  Target Schools
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Schools that match your academic profile well
                </p>
              </CardHeader>
              <CardContent>
                <SchoolSelectorDialog
                  selectedSchools={targetSchools}
                  onSchoolsChange={setTargetSchools}
                  triggerText="Add Target Schools"
                  title="Select Target Schools"
                  description="Choose schools that align with your academic credentials"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SchoolIcon className="h-5 w-5 text-green-500" />
                  Safety Schools
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Schools where you're likely to be admitted
                </p>
              </CardHeader>
              <CardContent>
                <SchoolSelectorDialog
                  selectedSchools={safetySchools}
                  onSchoolsChange={setSafetySchools}
                  triggerText="Add Safety Schools"
                  title="Select Safety Schools"
                  description="Choose schools where you have a strong chance of admission"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5 text-red-500" />
                  Reach Schools
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Ambitious choices that would be great to get into
                </p>
              </CardHeader>
              <CardContent>
                <SchoolSelectorDialog
                  selectedSchools={reachSchools}
                  onSchoolsChange={setReachSchools}
                  triggerText="Add Reach Schools"
                  title="Select Reach Schools"
                  description="Choose schools that are ambitious but achievable goals"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5 text-purple-500" />
                  Favorite Schools
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Schools you're particularly interested in
                </p>
              </CardHeader>
              <CardContent>
                <SchoolSelectorDialog
                  selectedSchools={favoriteSchools}
                  onSchoolsChange={setFavoriteSchools}
                  triggerText="Add Favorite Schools"
                  title="Select Favorite Schools"
                  description="Choose schools that particularly interest you"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4">
            {[
              { title: 'Target Schools', schools: targetSchools, color: 'blue' },
              { title: 'Safety Schools', schools: safetySchools, color: 'green' },
              { title: 'Reach Schools', schools: reachSchools, color: 'red' },
              { title: 'Favorite Schools', schools: favoriteSchools, color: 'purple' }
            ].map(({ title, schools, color }) => (
              <Card key={title}>
                <CardHeader>
                  <CardTitle>{title} ({schools.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  {schools.length === 0 ? (
                    <p className="text-muted-foreground text-sm">No schools selected yet</p>
                  ) : (
                    <div className="space-y-2">
                      {schools.map((school) => (
                        <div key={school.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <h4 className="font-medium">{school.name}</h4>
                            <p className="text-sm text-muted-foreground">
                              {school.city}, {school.state} • {school.ownership_label}
                            </p>
                          </div>
                          <div className="flex gap-2">
                            {school.admission_rate_percent && (
                              <Badge variant="outline">
                                {school.admission_rate_percent}% admission
                              </Badge>
                            )}
                            <Badge variant="secondary">
                              {school.student_size?.toLocaleString()} students
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

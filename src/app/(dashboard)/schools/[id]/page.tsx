'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, MapPin, Users, DollarSign, TrendingUp, ExternalLink, Globe } from 'lucide-react';
import { School } from '@/components/schools';

export default function SchoolDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [school, setSchool] = useState<School | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSchool = async () => {
      try {
        const response = await fetch(`/api/schools/public/${params.id}`);
        const data = await response.json();
        
        if (data.success) {
          setSchool(data.data);
        } else {
          setError(data.message || 'Failed to load school details');
        }
      } catch (err) {
        setError('Failed to load school details');
        console.error('Error fetching school:', err);
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchSchool();
    }
  }, [params.id]);

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <div className="flex items-center space-x-2">
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-8 w-32" />
        </div>
        <Skeleton className="h-12 w-3/4" />
        <div className="grid gap-4 md:grid-cols-2">
          <Skeleton className="h-48" />
          <Skeleton className="h-48" />
        </div>
      </div>
    );
  }

  if (error || !school) {
    return (
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">{error || 'School not found'}</p>
            <Button variant="outline" onClick={() => router.push('/schools')} className="mt-4">
              Browse Schools
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      {/* Header */}
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
      </div>

      {/* School Title */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">{school.name}</h1>
        <div className="flex items-center text-muted-foreground">
          <MapPin className="h-4 w-4 mr-1" />
          {school.city}, {school.state} {school.zip}
        </div>
      </div>

      {/* Quick Stats */}
      <div className="flex flex-wrap gap-2">
        <Badge variant="secondary" className="text-sm">
          {school.ownership_label}
        </Badge>
        {school.admission_rate_percent && (
          <Badge variant="outline" className="flex items-center gap-1">
            <TrendingUp className="h-3 w-3" />
            {school.admission_rate_percent}% admission rate
          </Badge>
        )}
        {school.student_size && (
          <Badge variant="outline" className="flex items-center gap-1">
            <Users className="h-3 w-3" />
            {school.student_size.toLocaleString()} students
          </Badge>
        )}
        {school.offers_bachelors && (
          <Badge variant="outline">
            Bachelor's Programs
          </Badge>
        )}
      </div>

      {/* Main Content */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Institution Type</label>
              <p className="text-sm">{school.ownership_label}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-muted-foreground">Location</label>
              <p className="text-sm">{school.city}, {school.state} {school.zip}</p>
            </div>

            {school.student_size && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Student Body Size</label>
                <p className="text-sm">{school.student_size.toLocaleString()} students</p>
              </div>
            )}

            {school.school_url && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Website</label>
                <div className="flex items-center space-x-2">
                  <Globe className="h-4 w-4 text-muted-foreground" />
                  <a 
                    href={school.school_url.startsWith('http') ? school.school_url : `https://${school.school_url}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:underline flex items-center"
                  >
                    {school.school_url}
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Academic & Financial Information */}
        <Card>
          <CardHeader>
            <CardTitle>Academic & Financial Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {school.admission_rate_percent && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Admission Rate</label>
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  <p className="text-sm">{school.admission_rate_percent}%</p>
                </div>
              </div>
            )}

            {school.tuition_display !== 'N/A' && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Tuition</label>
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <p className="text-sm">{school.tuition_display}</p>
                </div>
              </div>
            )}

            {school.tuition_in_state && school.tuition_out_of_state && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">In-State Tuition</label>
                  <p className="text-sm">${school.tuition_in_state.toLocaleString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Out-of-State Tuition</label>
                  <p className="text-sm">${school.tuition_out_of_state.toLocaleString()}</p>
                </div>
              </div>
            )}

            <div>
              <label className="text-sm font-medium text-muted-foreground">Bachelor's Programs</label>
              <p className="text-sm">{school.offers_bachelors ? 'Available' : 'Not Available'}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button variant="default">
              Add to Target Schools
            </Button>
            <Button variant="outline">
              Add to Safety Schools
            </Button>
            <Button variant="outline">
              Add to Reach Schools
            </Button>
            {school.school_url && (
              <Button variant="outline" asChild>
                <a 
                  href={school.school_url.startsWith('http') ? school.school_url : `https://${school.school_url}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Visit Website
                  <ExternalLink className="h-4 w-4 ml-2" />
                </a>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

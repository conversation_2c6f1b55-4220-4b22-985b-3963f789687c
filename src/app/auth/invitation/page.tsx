'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';

interface InvitationData {
  id: string;
  code: string;
  role: 'student' | 'consultant';
  email?: string;
  expires_at: string;
  max_uses: number;
  current_uses: number;
  notes?: string;
}

export default function InvitationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [invitationCode, setInvitationCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [invitationData, setInvitationData] = useState<InvitationData | null>(null);
  const [isValid, setIsValid] = useState(false);

  // Get invitation code from URL if provided
  useEffect(() => {
    const codeFromUrl = searchParams.get('code');
    if (codeFromUrl) {
      setInvitationCode(codeFromUrl);
      validateInvitation(codeFromUrl);
    }
  }, [searchParams]);

  const validateInvitation = async (code: string) => {
    if (!code.trim()) {
      toast.error('Please enter an invitation code');
      return;
    }

    setIsValidating(true);
    try {
      const response = await fetch('/api/invitations/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ code: code.trim().toUpperCase() })
      });

      const result = await response.json();

      if (result.success) {
        setInvitationData(result.data.invitation);
        setIsValid(true);
        toast.success('Invitation code is valid!');
      } else {
        setIsValid(false);
        setInvitationData(null);
        toast.error(result.message || 'Invalid invitation code');
      }
    } catch (error) {
      console.error('Error validating invitation:', error);
      setIsValid(false);
      setInvitationData(null);
      toast.error('Failed to validate invitation code');
    } finally {
      setIsValidating(false);
    }
  };

  const proceedToSignup = () => {
    if (!invitationData) return;
    
    // Store invitation data in sessionStorage for the signup process
    sessionStorage.setItem('invitation_data', JSON.stringify(invitationData));
    
    // Redirect to Clerk signup with invitation code
    router.push(`/auth/sign-up?invitation=${invitationData.code}`);
  };

  const getRoleBadge = (role: string) => {
    const variants = {
      student: 'default',
      consultant: 'secondary'
    } as const;
    
    return (
      <Badge variant={variants[role as keyof typeof variants] || 'default'}>
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Join Lighten Counsel
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Enter your invitation code to get started
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-center">Invitation Code</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="invitation-code">Invitation Code</Label>
              <Input
                id="invitation-code"
                type="text"
                placeholder="Enter your 8-character code"
                value={invitationCode}
                onChange={(e) => setInvitationCode(e.target.value.toUpperCase())}
                maxLength={8}
                className="text-center font-mono text-lg tracking-wider"
              />
            </div>

            <Button
              onClick={() => validateInvitation(invitationCode)}
              disabled={isValidating || !invitationCode.trim()}
              className="w-full"
            >
              {isValidating ? (
                <>
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  Validating...
                </>
              ) : (
                'Validate Code'
              )}
            </Button>

            {isValid && invitationData && (
              <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center mb-3">
                  <Icons.checkCircle className="h-5 w-5 text-green-500 mr-2" />
                  <span className="text-green-800 font-medium">Valid Invitation</span>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Role:</span>
                    {getRoleBadge(invitationData.role)}
                  </div>
                  
                  {invitationData.email && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Email:</span>
                      <span className="font-mono">{invitationData.email}</span>
                    </div>
                  )}
                  
                  <div className="flex justify-between">
                    <span className="text-gray-600">Expires:</span>
                    <span>{new Date(invitationData.expires_at).toLocaleDateString()}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-gray-600">Usage:</span>
                    <span>{invitationData.current_uses} / {invitationData.max_uses}</span>
                  </div>

                  {invitationData.notes && (
                    <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded">
                      <p className="text-blue-800 text-xs">{invitationData.notes}</p>
                    </div>
                  )}
                </div>

                <Button
                  onClick={proceedToSignup}
                  className="w-full mt-4"
                  size="lg"
                >
                  Continue to Sign Up
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-sm text-gray-600">
            Don't have an invitation code?{' '}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-500">
              Contact support
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}

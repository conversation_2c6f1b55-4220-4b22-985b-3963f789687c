'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';

interface InvitationData {
  id: string;
  code: string;
  role: 'student' | 'consultant';
  email?: string;
  expires_at: string;
  max_uses: number;
  current_uses: number;
  notes?: string;
}

export default function InvitationCompletePage() {
  const router = useRouter();
  const { user, isLoaded } = useUser();
  const [isProcessing, setIsProcessing] = useState(true);
  const [invitationData, setInvitationData] = useState<InvitationData | null>(null);
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');

  useEffect(() => {
    const processInvitation = async () => {
      if (!isLoaded || !user) return;

      try {
        // Get invitation data from sessionStorage
        const storedData = sessionStorage.getItem('invitation_data');
        if (!storedData) {
          throw new Error('No invitation data found');
        }

        const invitation: InvitationData = JSON.parse(storedData);
        setInvitationData(invitation);

        // Create registration request with Google account data
        const registrationResponse = await fetch('/api/registration-requests', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            email: user.primaryEmailAddress?.emailAddress,
            invitation_code: invitation.code,
            role_requested: invitation.role,
            profile_data: {
              first_name: user.firstName,
              last_name: user.lastName,
              google_id: user.externalId
            }
          })
        });

        const registrationResult = await registrationResponse.json();

        if (registrationResult.success) {
          // Clear stored invitation data
          sessionStorage.removeItem('invitation_data');
          setStatus('success');
          toast.success('Account created! Waiting for admin approval.');
          
          // Redirect to dashboard after a delay
          setTimeout(() => {
            router.push('/dashboard');
          }, 3000);
        } else {
          throw new Error(registrationResult.message || 'Failed to create registration request');
        }
      } catch (error: any) {
        console.error('Registration error:', error);
        setStatus('error');
        toast.error(error.message || 'Failed to complete registration');
      } finally {
        setIsProcessing(false);
      }
    };

    processInvitation();
  }, [isLoaded, user, router]);

  if (!isLoaded || isProcessing) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Icons.spinner className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Processing your invitation...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {status === 'success' && (
          <Card>
            <CardHeader>
              <CardTitle className="text-center text-green-600">
                <Icons.checkCircle className="mx-auto h-12 w-12 mb-2" />
                Account Created Successfully!
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground mb-4">
                Your account has been created and your invitation has been processed.
                You will be redirected to the dashboard shortly.
              </p>
              <div className="flex items-center justify-center">
                <Icons.spinner className="h-4 w-4 animate-spin mr-2" />
                <span className="text-sm">Redirecting...</span>
              </div>
            </CardContent>
          </Card>
        )}

        {status === 'error' && (
          <Card>
            <CardHeader>
              <CardTitle className="text-center text-red-600">
                <Icons.alertCircle className="mx-auto h-12 w-12 mb-2" />
                Registration Failed
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground mb-4">
                There was an error processing your invitation. Please try again or contact support.
              </p>
              <Button
                onClick={() => router.push('/auth/invitation')}
                className="w-full"
              >
                Try Again
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

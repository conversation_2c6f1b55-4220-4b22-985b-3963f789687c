'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useClerk } from '@clerk/nextjs';
import { Icons } from '@/components/icons';

export default function SSOCallbackPage() {
  const router = useRouter();
  const { handleRedirectCallback } = useClerk();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        await handleRedirectCallback();
        // After successful OAuth, redirect to invitation completion
        router.push('/auth/invitation-complete');
      } catch (error) {
        console.error('OAuth callback error:', error);
        router.push('/auth/invitation?error=oauth_failed');
      }
    };

    handleCallback();
  }, [handleRedirectCallback, router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <Icons.spinner className="h-8 w-8 animate-spin mx-auto mb-4" />
        <p className="text-muted-foreground">Completing sign up...</p>
      </div>
    </div>
  );
}

import { NextRequest } from 'next/server';
import {
  withAuth,
  withR<PERSON>,
  createSuccessResponse,
  parseRequestBody,
  NotFoundError,
  AuthorizationError
} from '@/lib/api-utils';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';

interface RouteContext {
  params: { id: string };
}

// GET /api/users/[id] - Get user by ID
export const GET = withAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    const params = await context.params;
    const { id } = params;

    // Users can only view their own profile unless they're admin
    if (id !== currentUser.id && currentUser.role !== 'admin') {
      throw new AuthorizationError('You can only view your own profile');
    }

    const { data: user, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Database error fetching user:', error);
      throw new NotFoundError(`User not found: ${error.message}`);
    }

    if (!user) {
      console.error('No user found with ID:', id);
      throw new NotFoundError('User not found');
    }

    return createSuccessResponse(user, 'User retrieved successfully');
  }
);

// PUT /api/users/[id] - Update user
export const PUT = withAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    const params = await context.params;
    const { id } = params;

    // Users can only update their own profile unless they're admin
    if (id !== currentUser.id && currentUser.role !== 'admin') {
      throw new AuthorizationError('You can only update your own profile');
    }

    const body = await parseRequestBody<{
      email?: string;
      role?: 'student' | 'consultant' | 'admin';
      profile_data?: any;
    }>(request);

    // Only admins can change roles
    if (body.role && currentUser.role !== 'admin') {
      throw new AuthorizationError('Only admins can change user roles');
    }

    const { data: user, error } = await supabaseAdmin
      .from('users')
      .update({
        ...body,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    if (!user) {
      throw new NotFoundError('User not found');
    }

    return createSuccessResponse(user, 'User updated successfully');
  }
);

// DELETE /api/users/[id] - Delete user (Admin only)
export const DELETE = withRole(
  ['admin'],
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    const params = await context.params;
    const { id } = params;

    // Prevent admin from deleting themselves
    if (id === currentUser.id) {
      throw new AuthorizationError('You cannot delete your own account');
    }

    const { error } = await supabaseAdmin.from('users').delete().eq('id', id);

    if (error) {
      throw error;
    }

    return createSuccessResponse(null, 'User deleted successfully');
  }
);

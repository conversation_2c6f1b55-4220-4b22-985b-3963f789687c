import { NextRequest } from 'next/server';
import { withAuth } from '@/lib/auth-middleware';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { createSuccessResponse, createErrorResponse } from '@/lib/api-response';
import { User } from '@/types/application';

// POST /api/users/[id]/deactivate - Deactivate a user (admin only)
export const POST = withAuth(
  async (request: NextRequest, currentUser: User, { params }: { params: Promise<{ id: string }> }) => {
    try {
      // Only admins can deactivate users
      if (currentUser.role !== 'admin') {
        return createErrorResponse('Unauthorized', 403);
      }

      const resolvedParams = await params;
      const userId = resolvedParams.id;

      // Check if user exists
      const { data: existingUser, error: fetchError } = await supabaseAdmin
        .from('users')
        .select('id, email, role')
        .eq('id', userId)
        .single();

      if (fetchError || !existingUser) {
        return createErrorResponse('User not found', 404);
      }

      // Prevent deactivating other admins
      if (existingUser.role === 'admin') {
        return createErrorResponse('Cannot deactivate admin users', 403);
      }

      // Update user profile_data to mark as inactive
      const { error: updateError } = await supabaseAdmin
        .from('users')
        .update({
          profile_data: {
            ...existingUser.profile_data || {},
            status: 'inactive',
            deactivated_at: new Date().toISOString(),
            deactivated_by: currentUser.id
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (updateError) {
        throw updateError;
      }

      return createSuccessResponse(
        { user_id: userId },
        'User deactivated successfully'
      );
    } catch (error) {
      console.error('Error deactivating user:', error);
      return createErrorResponse('Failed to deactivate user', 500);
    }
  }
);

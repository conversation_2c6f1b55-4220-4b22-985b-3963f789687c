import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { handleAsyncOperation, createStandardSuccessResponse } from '@/lib/api-utils';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  return handleAsyncOperation(async () => {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const state = searchParams.get('state');
    const ownership = searchParams.get('ownership');
    const bachelorsOnly = searchParams.get('bachelors') === 'true';
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100);
    const offset = parseInt(searchParams.get('offset') || '0');

    let supabaseQuery = supabase
      .from('schools')
      .select(`
        id,
        scorecard_id,
        name,
        city,
        state,
        zip,
        school_url,
        ownership,
        admission_rate,
        tuition_in_state,
        tuition_out_of_state,
        student_size,
        offers_bachelors
      `)
      .order('name');

    // Apply filters
    if (query.trim()) {
      // Use full-text search for school names
      supabaseQuery = supabaseQuery.textSearch('name', query, {
        type: 'websearch',
        config: 'english'
      });
    }

    if (state) {
      supabaseQuery = supabaseQuery.eq('state', state.toUpperCase());
    }

    if (ownership) {
      supabaseQuery = supabaseQuery.eq('ownership', parseInt(ownership));
    }

    if (bachelorsOnly) {
      supabaseQuery = supabaseQuery.eq('offers_bachelors', true);
    }

    // Apply pagination
    supabaseQuery = supabaseQuery.range(offset, offset + limit - 1);

    const { data: schools, error, count } = await supabaseQuery;

    if (error) {
      throw new Error(`Failed to search schools: ${error.message}`);
    }

    // Format the results with additional computed fields
    const formattedSchools = schools?.map(school => ({
      ...school,
      ownership_label: getOwnershipLabel(school.ownership),
      tuition_display: getTuitionDisplay(school.tuition_in_state, school.tuition_out_of_state),
      admission_rate_percent: school.admission_rate ? Math.round(school.admission_rate * 100) : null
    })) || [];

    return createStandardSuccessResponse(
      {
        schools: formattedSchools,
        total: count,
        limit,
        offset,
        hasMore: count ? offset + limit < count : false
      },
      `Found ${formattedSchools.length} schools`
    );
  });
}

function getOwnershipLabel(ownership: number | null): string {
  switch (ownership) {
    case 1: return 'Public';
    case 2: return 'Private Nonprofit';
    case 3: return 'Private For-Profit';
    default: return 'Unknown';
  }
}

function getTuitionDisplay(inState: number | null, outOfState: number | null): string {
  if (!inState && !outOfState) return 'N/A';
  if (inState && outOfState && inState !== outOfState) {
    return `$${inState.toLocaleString()} (in-state) / $${outOfState.toLocaleString()} (out-of-state)`;
  }
  if (inState) return `$${inState.toLocaleString()}`;
  if (outOfState) return `$${outOfState.toLocaleString()}`;
  return 'N/A';
}

import { NextRequest } from 'next/server';
import { createStandardSuccessResponse, handleAsyncOperation, NotFoundError } from '@/lib/api-utils';
import { supabase } from '@/lib/supabase';

interface RouteContext {
  params: { id: string };
}

// GET /api/schools/public/[id] - Get school by ID (public endpoint)
export async function GET(request: NextRequest, context: RouteContext) {
  return handleAsyncOperation(async () => {
    const { id } = context.params;

    const { data: school, error } = await supabase
      .from('schools')
      .select(`
        id,
        scorecard_id,
        name,
        city,
        state,
        zip,
        school_url,
        ownership,
        admission_rate,
        tuition_in_state,
        tuition_out_of_state,
        student_size,
        offers_bachelors,
        created_at,
        updated_at
      `)
      .eq('id', id)
      .single();

    if (error || !school) {
      throw new NotFoundError('School not found');
    }

    // Format the school data with additional computed fields
    const formattedSchool = {
      ...school,
      ownership_label: getOwnershipLabel(school.ownership),
      tuition_display: getTuitionDisplay(school.tuition_in_state, school.tuition_out_of_state),
      admission_rate_percent: school.admission_rate ? Math.round(school.admission_rate * 100) : null,
      student_size_display: school.student_size ? school.student_size.toLocaleString() : 'N/A'
    };

    return createStandardSuccessResponse(
      formattedSchool,
      'School retrieved successfully'
    );
  }, 'Failed to retrieve school');
}

// Helper functions
function getOwnershipLabel(ownership: number | null): string {
  switch (ownership) {
    case 1: return 'Public';
    case 2: return 'Private Nonprofit';
    case 3: return 'Private For-Profit';
    default: return 'Unknown';
  }
}

function getTuitionDisplay(inState: number | null, outOfState: number | null): string {
  if (!inState && !outOfState) return 'N/A';
  if (inState && outOfState && inState !== outOfState) {
    return `$${inState.toLocaleString()} (in-state) / $${outOfState.toLocaleString()} (out-of-state)`;
  }
  if (inState) return `$${inState.toLocaleString()}`;
  if (outOfState) return `$${outOfState.toLocaleString()}`;
  return 'N/A';
}

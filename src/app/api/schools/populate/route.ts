import { NextRequest } from 'next/server';
import {
  withStandardRole,
  createStandardSuccessResponse,
  handleAsyncOperation,
  parseRequestBody
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';

const COLLEGE_SCORECARD_API_KEY = 'S4O6fWnYaGxZR86ghIhbsb9XTrr72eVaBQsJjfvu';
const COLLEGE_SCORECARD_BASE_URL = 'https://api.data.gov/ed/collegescorecard/v1/schools';

interface CollegeScorecardSchool {
  id: number;
  'school.name': string | null;
  'school.city': string | null;
  'school.state': string | null;
  'school.zip': string | null;
  'school.school_url': string | null;
  'school.ownership': number | null; // 1=Public, 2=Private nonprofit, 3=Private for-profit
  'school.locale': number | null;
  'school.ccbasic': number | null; // Carnegie Classification
  'latest.admissions.admission_rate.overall': number | null;
  'latest.cost.tuition.in_state': number | null;
  'latest.cost.tuition.out_of_state': number | null;
  'latest.student.size': number | null;
  'latest.academics.program_available.assoc': boolean | null;
  'latest.academics.program_available.bachelors': boolean | null;
  'latest.academics.program_available.masters': boolean | null;
  'latest.academics.program_available.doctoral': boolean | null;
}

// Helper function to map ownership code to readable type
function getSchoolType(ownership: number | null): string {
  if (ownership === null) return 'Unknown';
  switch (ownership) {
    case 1: return 'Public University';
    case 2: return 'Private University';
    case 3: return 'Private For-Profit';
    default: return 'Unknown';
  }
}

// Helper function to transform College Scorecard data to our schema
function transformSchoolData(scorecardSchool: CollegeScorecardSchool) {
  // Handle null values safely
  const city = scorecardSchool['school.city'] || '';
  const state = scorecardSchool['school.state'] || '';
  const location = city && state ? `${city}, ${state}` : (city || state || 'Unknown');

  return {
    name: scorecardSchool['school.name'] || 'Unknown School',
    details: {
      location,
      type: getSchoolType(scorecardSchool['school.ownership']),
      acceptance_rate: scorecardSchool['latest.admissions.admission_rate.overall'] ?
        Math.round(scorecardSchool['latest.admissions.admission_rate.overall'] * 100) : null,
      tuition: scorecardSchool['latest.cost.tuition.out_of_state'] ||
               scorecardSchool['latest.cost.tuition.in_state'] || null,
      website: scorecardSchool['school.school_url'] || null,
      student_size: scorecardSchool['latest.student.size'] || null,
      city: scorecardSchool['school.city'] || null,
      state: scorecardSchool['school.state'] || null,
      zip: scorecardSchool['school.zip'] || null,
      scorecard_id: scorecardSchool.id,
      programs: {
        associates: scorecardSchool['latest.academics.program_available.assoc'] === true,
        bachelors: scorecardSchool['latest.academics.program_available.bachelors'] === true,
        masters: scorecardSchool['latest.academics.program_available.masters'] === true,
        doctoral: scorecardSchool['latest.academics.program_available.doctoral'] === true
      }
    },
    application_requirements: {
      // Default requirements - can be enhanced later
      essays: [],
      test_scores_required: ['SAT', 'ACT'],
      transcript_required: true,
      recommendation_letters: 2
    }
  };
}

// POST /api/schools/populate - Populate schools from College Scorecard API (Admin only)
export const POST = withStandardRole(
  ['admin'],
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await parseRequestBody<{
        limit?: number;
        state?: string;
        search?: string;
        page?: number;
      }>(request);

      const limit = Math.min(body.limit || 100, 500); // Max 500 schools per request
      const page = body.page || 0;
      
      // Build API URL with parameters
      const params = new URLSearchParams({
        api_key: COLLEGE_SCORECARD_API_KEY,
        per_page: limit.toString(),
        page: page.toString(),
        fields: [
          'id',
          'school.name',
          'school.city',
          'school.state',
          'school.zip',
          'school.school_url',
          'school.ownership',
          'school.locale',
          'school.ccbasic',
          'latest.admissions.admission_rate.overall',
          'latest.cost.tuition.in_state',
          'latest.cost.tuition.out_of_state',
          'latest.student.size',
          'latest.academics.program_available.assoc',
          'latest.academics.program_available.bachelors',
          'latest.academics.program_available.masters',
          'latest.academics.program_available.doctoral'
        ].join(',')
      });

      // Add filters if provided
      if (body.state) {
        params.append('school.state', body.state);
      }
      
      if (body.search) {
        params.append('school.name', body.search);
      }

      // Only get schools that offer bachelor's degrees (most relevant for college applications)
      params.append('latest.academics.program_available.bachelors', 'true');
      
      // Only get currently operating schools
      params.append('school.operating', '1');

      const apiUrl = `${COLLEGE_SCORECARD_BASE_URL}?${params.toString()}`;
      
      console.log('Fetching from College Scorecard API:', apiUrl);

      // Fetch data from College Scorecard API
      const response = await fetch(apiUrl);
      
      if (!response.ok) {
        throw new Error(`College Scorecard API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const schools = data.results as CollegeScorecardSchool[];

      if (!schools || schools.length === 0) {
        return createStandardSuccessResponse(
          { imported: 0, skipped: 0, errors: 0 },
          'No schools found matching criteria',
          200
        );
      }

      // Transform and insert schools
      let imported = 0;
      let skipped = 0;
      let errors = 0;
      const errorDetails: string[] = [];

      for (const scorecardSchool of schools) {
        try {
          // Skip schools without names
          if (!scorecardSchool['school.name'] || scorecardSchool['school.name'].trim() === '') {
            skipped++;
            continue;
          }

          // Check if school already exists
          const { data: existingSchool } = await supabaseAdmin
            .from('schools')
            .select('id')
            .eq('name', scorecardSchool['school.name'])
            .single();

          if (existingSchool) {
            skipped++;
            continue;
          }

          // Transform and insert school
          const schoolData = transformSchoolData(scorecardSchool);
          
          const { error } = await supabaseAdmin
            .from('schools')
            .insert(schoolData);

          if (error) {
            errors++;
            errorDetails.push(`${scorecardSchool['school.name']}: ${error.message}`);
          } else {
            imported++;
          }
        } catch (error) {
          errors++;
          errorDetails.push(`${scorecardSchool['school.name']}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      return createStandardSuccessResponse(
        {
          imported,
          skipped,
          errors,
          total_processed: schools.length,
          error_details: errorDetails.slice(0, 10) // Limit error details
        },
        `Successfully processed ${schools.length} schools. Imported: ${imported}, Skipped: ${skipped}, Errors: ${errors}`,
        200,
        {
          user_role: currentUser.role,
          operation_type: 'bulk_import',
          resource_type: 'schools',
          admin_operation: true,
          api_source: 'college_scorecard'
        }
      );
    }, 'Failed to populate schools from College Scorecard API');
  }
);

// GET /api/schools/populate - Get available states and stats (Admin only)
export const GET = withStandardRole(
  ['admin'],
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      // Get current school count
      const { count: currentSchoolCount } = await supabaseAdmin
        .from('schools')
        .select('*', { count: 'exact', head: true });

      // Common US states for filtering
      const availableStates = [
        'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA',
        'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD',
        'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ',
        'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC',
        'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
      ];

      return createStandardSuccessResponse(
        {
          current_school_count: currentSchoolCount || 0,
          available_states: availableStates,
          api_limits: {
            max_per_request: 500,
            recommended_per_request: 100
          },
          usage_tips: [
            'Start with a specific state to avoid overwhelming the database',
            'Use smaller batches (50-100) for better performance',
            'Popular states like CA, NY, TX have many schools'
          ]
        },
        'School population information retrieved',
        200
      );
    }, 'Failed to get school population information');
  }
);

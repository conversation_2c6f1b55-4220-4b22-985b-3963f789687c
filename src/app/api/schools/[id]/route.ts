import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  withStandardRole,
  createStandardSuccessResponse,
  parseRequestBody,
  handleAsyncOperation,
  NotFoundError
} from '@/lib/api-utils';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import type {
  User,
  SchoolDetails,
  ApplicationRequirements
} from '@/types/application';

interface RouteContext {
  params: { id: string };
}

// GET /api/schools/[id] - Get school by ID
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      const { data: school, error } = await supabase
        .from('schools')
        .select('*')
        .eq('id', id)
        .single();

      if (error || !school) {
        throw new NotFoundError('School not found');
      }

      return createStandardSuccessResponse(
        school,
        'School retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'read',
          resource_type: 'school',
          school_id: id
        }
      );
    }, 'Failed to retrieve school');
  }
);

// PUT /api/schools/[id] - Update school (Admin only)
export const PUT = withStandardRole(
  ['admin'],
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const params = await context.params;
      const { id } = params;

      const body = await parseRequestBody<{
        name?: string;
        details?: SchoolDetails;
        application_requirements?: ApplicationRequirements;
      }>(request);

      const { data: school, error } = await supabaseAdmin
        .from('schools')
        .update({
          ...body,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      if (!school) {
        throw new NotFoundError('School not found');
      }

      return createStandardSuccessResponse(
        school,
        'School updated successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'update',
          resource_type: 'school',
          school_id: id,
          admin_operation: true,
          updated_fields: Object.keys(body)
        }
      );
    }, 'Failed to update school');
  }
);

// DELETE /api/schools/[id] - Delete school (Admin only)
export const DELETE = withStandardRole(
  ['admin'],
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const params = await context.params;
      const { id } = params;

      const { error } = await supabaseAdmin
        .from('schools')
        .delete()
        .eq('id', id);

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        null,
        'School deleted successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'delete',
          resource_type: 'school',
          school_id: id,
          admin_operation: true
        }
      );
    }, 'Failed to delete school');
  }
);

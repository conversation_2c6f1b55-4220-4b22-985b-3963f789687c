import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { handleAsyncOperation, createStandardSuccessResponse } from '@/lib/api-utils';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const COLLEGE_SCORECARD_API_KEY = 'S4O6fWnYaGxZR86ghIhbsb9XTrr72eVaBQsJjfvu';
const COLLEGE_SCORECARD_BASE_URL = 'https://api.data.gov/ed/collegescorecard/v1/schools';

interface CollegeData {
  id: number;
  'school.name': string;
  'school.city': string;
  'school.state': string;
  'school.zip': string;
  'school.school_url': string;
  'school.ownership': number;
  'latest.admissions.admission_rate.overall': number;
  'latest.cost.tuition.in_state': number;
  'latest.cost.tuition.out_of_state': number;
  'latest.student.size': number;
  'latest.academics.program_available.bachelors': number;
}

interface CollegeScorecardResponse {
  metadata: {
    total: number;
    page: number;
    per_page: number;
  };
  results: CollegeData[];
}

async function fetchCollegeData(page: number = 0, perPage: number = 100): Promise<CollegeScorecardResponse> {
  const params = new URLSearchParams({
    api_key: COLLEGE_SCORECARD_API_KEY,
    per_page: perPage.toString(),
    page: page.toString(),
    fields: [
      'id',
      'school.name',
      'school.city', 
      'school.state',
      'school.zip',
      'school.school_url',
      'school.ownership',
      'latest.admissions.admission_rate.overall',
      'latest.cost.tuition.in_state',
      'latest.cost.tuition.out_of_state',
      'latest.student.size',
      'latest.academics.program_available.bachelors'
    ].join(',')
  });

  // Only include operating schools
  params.append('school.operating', '1');

  const url = `${COLLEGE_SCORECARD_BASE_URL}?${params.toString()}`;
  console.log(`Fetching from College Scorecard API: ${url}`);

  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`College Scorecard API error: ${response.status} ${response.statusText}`);
  }

  return await response.json();
}

async function insertSchoolsBatch(schools: CollegeData[]): Promise<void> {
  const schoolsToInsert = schools
    .filter(school => school.id && school['school.name']) // Only include schools with ID and name
    .map(school => ({
      scorecard_id: school.id,
      name: school['school.name'],
      city: school['school.city'] || null,
      state: school['school.state'] || null,
      zip: school['school.zip'] || null,
      school_url: school['school.school_url'] || null,
      ownership: school['school.ownership'] || null,
      admission_rate: school['latest.admissions.admission_rate.overall'] || null,
      tuition_in_state: school['latest.cost.tuition.in_state'] || null,
      tuition_out_of_state: school['latest.cost.tuition.out_of_state'] || null,
      student_size: school['latest.student.size'] || null,
      offers_bachelors: school['latest.academics.program_available.bachelors'] === 1
    }));

  if (schoolsToInsert.length === 0) {
    console.log('No valid schools to insert in this batch');
    return;
  }

  const { error } = await supabase
    .from('schools')
    .upsert(schoolsToInsert, { 
      onConflict: 'scorecard_id',
      ignoreDuplicates: false 
    });

  if (error) {
    console.error('Error inserting schools:', error);
    throw new Error(`Failed to insert schools: ${error.message}`);
  }

  console.log(`Successfully inserted/updated ${schoolsToInsert.length} schools`);
}

export async function POST(request: NextRequest) {
  return handleAsyncOperation(async () => {
    const { searchParams } = new URL(request.url);
    const maxPages = parseInt(searchParams.get('maxPages') || '10');
    const perPage = parseInt(searchParams.get('perPage') || '100');
    const startPage = parseInt(searchParams.get('startPage') || '0');

    console.log(`Starting school population: pages ${startPage} to ${startPage + maxPages - 1}, ${perPage} per page`);

    let totalInserted = 0;
    let currentPage = startPage;

    for (let i = 0; i < maxPages; i++) {
      try {
        console.log(`Fetching page ${currentPage}...`);
        const data = await fetchCollegeData(currentPage, perPage);
        
        if (data.results.length === 0) {
          console.log('No more results, stopping pagination');
          break;
        }

        await insertSchoolsBatch(data.results);
        totalInserted += data.results.length;
        currentPage++;

        console.log(`Completed page ${currentPage - 1}. Total schools processed: ${totalInserted}`);
        
        // Add a small delay to be respectful to the API
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.error(`Error processing page ${currentPage}:`, error);
        throw error;
      }
    }

    // Get final count from database
    const { count } = await supabase
      .from('schools')
      .select('*', { count: 'exact', head: true });

    return createStandardSuccessResponse(
      {
        pagesProcessed: currentPage - startPage,
        schoolsProcessed: totalInserted,
        totalSchoolsInDatabase: count
      },
      `Successfully populated schools database with ${totalInserted} schools`
    );
  });
}

export async function GET() {
  return handleAsyncOperation(async () => {
    // Get current count of schools in database
    const { count } = await supabase
      .from('schools')
      .select('*', { count: 'exact', head: true });

    return createStandardSuccessResponse(
      { totalSchools: count },
      'Use POST method to populate schools data'
    );
  });
}

import { NextRequest } from 'next/server';
import {
  withStandardRole,
  createStandardSuccessResponse,
  handleAsyncOperation,
  parseRequestBody,
  validateRequiredFields
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';
import { EmailService } from '@/lib/email-service';

interface RouteContext {
  params: {
    id: string;
  };
}

// POST /api/invitations/[id]/send-email - Send invitation email (Admin only)
export const POST = withStandardRole(
  ['admin'],
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;
      const body = await parseRequestBody<{
        email: string;
      }>(request);

      validateRequiredFields(body, ['email']);

      // Get invitation details
      const { data: invitation, error } = await supabaseAdmin
        .from('invitations')
        .select('*')
        .eq('id', id)
        .single();

      if (error || !invitation) {
        throw new Error('Invitation not found');
      }

      // Check if invitation is still valid
      const now = new Date();
      const expiresAt = new Date(invitation.expires_at);
      if (now > expiresAt) {
        throw new Error('Cannot send email for expired invitation');
      }

      if (invitation.status !== 'pending') {
        throw new Error('Cannot send email for non-pending invitation');
      }

      // Get inviter name for email
      const { data: inviterProfile } = await supabaseAdmin
        .from('users')
        .select('profile_data')
        .eq('id', currentUser.id)
        .single();

      const inviterName = inviterProfile?.profile_data?.firstName && inviterProfile?.profile_data?.lastName
        ? `${inviterProfile.profile_data.firstName} ${inviterProfile.profile_data.lastName}`
        : inviterProfile?.profile_data?.name || 'Admin';

      // Send invitation email
      await EmailService.sendInvitationEmail({
        to: body.email,
        invitationCode: invitation.code,
        role: invitation.role,
        expiresAt: invitation.expires_at,
        notes: invitation.notes,
        inviterName
      });

      // Update invitation with email sent info (optional)
      await supabaseAdmin
        .from('invitations')
        .update({
          email: body.email,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      return createStandardSuccessResponse(
        { invitation_id: id, email: body.email },
        `Invitation email sent successfully to ${body.email}`,
        200,
        {
          operation_type: 'send_email'
        }
      );
    }, 'Failed to send invitation email');
  }
);

import { NextRequest } from 'next/server';
import {
  createStandardSuccessResponse,
  createStandardErrorResponse,
  handleAsyncOperation,
  parseRequestBody,
  validateRequiredFields,
  NotFoundError,
  ValidationError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';

// POST /api/invitations/validate - Validate invitation code (Public)
export const POST = async (request: NextRequest) => {
  return handleAsyncOperation(async () => {
    const body = await parseRequestBody<{
      code: string;
      email?: string;
    }>(request);

    validateRequiredFields(body, ['code']);

    // Get invitation details
    const { data: invitation, error } = await supabaseAdmin
      .from('invitations')
      .select('*')
      .eq('code', body.code.toUpperCase())
      .single();

    if (error || !invitation) {
      throw new NotFoundError('Invalid invitation code');
    }

    // Check if invitation is expired
    const now = new Date();
    const expiresAt = new Date(invitation.expires_at);
    if (now > expiresAt) {
      throw new ValidationError('Invitation code has expired');
    }

    // Check if invitation is already used up
    if (invitation.current_uses >= invitation.max_uses) {
      throw new ValidationError('Invitation code has reached maximum usage limit');
    }

    // Check if invitation status is valid
    if (invitation.status !== 'pending') {
      throw new ValidationError('Invitation code is no longer valid');
    }

    // If invitation is for a specific email, validate it
    if (invitation.email && body.email) {
      if (invitation.email.toLowerCase() !== body.email.toLowerCase()) {
        throw new ValidationError('This invitation is for a different email address');
      }
    }

    return createStandardSuccessResponse(
      {
        valid: true,
        invitation: {
          id: invitation.id,
          code: invitation.code,
          role: invitation.role,
          email: invitation.email,
          expires_at: invitation.expires_at,
          max_uses: invitation.max_uses,
          current_uses: invitation.current_uses,
          notes: invitation.notes
        }
      },
      'Invitation code is valid',
      200,
      {
        operation_type: 'validate',
        remaining_uses: invitation.max_uses - invitation.current_uses
      }
    );
  }, 'Failed to validate invitation code');
};

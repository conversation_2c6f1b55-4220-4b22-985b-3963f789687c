import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  withStandardRole,
  createStandardSuccessResponse,
  createStandardPaginatedResponse,
  parsePaginationParams,
  handleAsyncOperation,
  parseRequestBody,
  validateRequiredFields,
  AuthorizationError
} from '@/lib/api-utils';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';
import { EmailService } from '@/lib/email-service';

// Helper function to generate invitation code
function generateInvitationCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// GET /api/invitations - List invitations (Admin only)
export const GET = withStandardRole(
  ['admin'],
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const pagination = parsePaginationParams(request);
      const url = new URL(request.url);
      const status = url.searchParams.get('status');
      const role = url.searchParams.get('role');

      let query = supabaseAdmin
        .from('invitations')
        .select('*', { count: 'exact' })
        .range(pagination.offset, pagination.offset + pagination.limit - 1)
        .order('created_at', { ascending: false });

      // Apply filters
      if (status) {
        query = query.eq('status', status);
      }
      if (role) {
        query = query.eq('role', role);
      }

      const { data, error, count } = await query;

      if (error) {
        throw error;
      }

      return createStandardPaginatedResponse(
        data || [],
        count || 0,
        pagination,
        'Invitations retrieved successfully',
        {
          total_invitations: count || 0,
          filters: { status, role },
          operation_type: 'list'
        }
      );
    }, 'Failed to retrieve invitations');
  }
);

// POST /api/invitations - Create invitation (Admin only)
export const POST = withStandardRole(
  ['admin'],
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await parseRequestBody<{
        email?: string; // For specific email invitations
        firstName?: string; // First name for invitation
        lastName?: string; // Last name for invitation
        role: 'student' | 'consultant';
        expires_in_days?: number;
        max_uses?: number;
        notes?: string;
      }>(request);

      validateRequiredFields(body, ['role']);

      // Generate unique invitation code
      let code: string;
      let isUnique = false;
      let attempts = 0;
      const maxAttempts = 10;

      do {
        code = generateInvitationCode();
        const { data: existing } = await supabaseAdmin
          .from('invitations')
          .select('id')
          .eq('code', code)
          .single();
        
        isUnique = !existing;
        attempts++;
      } while (!isUnique && attempts < maxAttempts);

      if (!isUnique) {
        throw new Error('Failed to generate unique invitation code');
      }

      // Calculate expiration date
      const expiresInDays = body.expires_in_days || 30;
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + expiresInDays);

      const invitationData = {
        code: code!,
        email: body.email || null,
        first_name: body.firstName || null,
        last_name: body.lastName || null,
        role: body.role,
        created_by: currentUser.id,
        expires_at: expiresAt.toISOString(),
        max_uses: body.max_uses || 1,
        current_uses: 0,
        notes: body.notes || null,
        status: 'pending' as const
      };

      const { data: invitation, error } = await supabaseAdmin
        .from('invitations')
        .insert(invitationData)
        .select('*')
        .single();

      if (error) {
        throw error;
      }

      // Send invitation email if email is provided
      let emailSent = false;
      if (body.email) {
        try {
          // Get inviter name for email
          const { data: inviterProfile } = await supabaseAdmin
            .from('users')
            .select('profile_data')
            .eq('id', currentUser.id)
            .single();

          const inviterName = inviterProfile?.profile_data?.firstName && inviterProfile?.profile_data?.lastName
            ? `${inviterProfile.profile_data.firstName} ${inviterProfile.profile_data.lastName}`
            : inviterProfile?.profile_data?.name || 'Admin';

          await EmailService.sendInvitationEmail({
            to: body.email,
            invitationCode: code!,
            role: body.role,
            expiresAt: expiresAt.toISOString(),
            notes: body.notes,
            inviterName
          });
          emailSent = true;
        } catch (emailError) {
          console.error('Failed to send invitation email:', emailError);
          // Don't fail the invitation creation if email fails
        }
      }

      return createStandardSuccessResponse(
        invitation,
        emailSent
          ? `Invitation created and email sent to ${body.email}`
          : 'Invitation created successfully',
        201,
        {
          invitation_code: code!,
          expires_at: expiresAt.toISOString(),
          email_sent: emailSent,
          operation_type: 'create'
        }
      );
    }, 'Failed to create invitation');
  }
);

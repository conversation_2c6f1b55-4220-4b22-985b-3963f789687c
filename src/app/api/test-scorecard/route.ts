import { NextRequest } from 'next/server';
import { createStandardSuccessResponse, handleAsyncOperation } from '@/lib/api-utils';

const COLLEGE_SCORECARD_API_KEY = 'S4O6fWnYaGxZR86ghIhbsb9XTrr72eVaBQsJjfvu';
const COLLEGE_SCORECARD_BASE_URL = 'https://api.data.gov/ed/collegescorecard/v1/schools';

interface CollegeScorecardSchool {
  id: number;
  'school.name': string | null;
  'school.city': string | null;
  'school.state': string | null;
  'school.zip': string | null;
  'school.school_url': string | null;
  'school.ownership': number | null;
  'latest.admissions.admission_rate.overall': number | null;
  'latest.cost.tuition.in_state': number | null;
  'latest.cost.tuition.out_of_state': number | null;
  'latest.student.size': number | null;
  'latest.academics.program_available.bachelors': boolean | null;
}

function getSchoolType(ownership: number | null): string {
  if (ownership === null) return 'Unknown';
  switch (ownership) {
    case 1: return 'Public University';
    case 2: return 'Private University';
    case 3: return 'Private For-Profit';
    default: return 'Unknown';
  }
}

function transformSchoolData(scorecardSchool: CollegeScorecardSchool) {
  const city = scorecardSchool['school.city'] || '';
  const state = scorecardSchool['school.state'] || '';
  const location = city && state ? `${city}, ${state}` : (city || state || 'Unknown');
  
  return {
    name: scorecardSchool['school.name'] || 'Unknown School',
    details: {
      location,
      type: getSchoolType(scorecardSchool['school.ownership']),
      acceptance_rate: scorecardSchool['latest.admissions.admission_rate.overall'] ? 
        Math.round(scorecardSchool['latest.admissions.admission_rate.overall'] * 100) : null,
      tuition: scorecardSchool['latest.cost.tuition.out_of_state'] || 
               scorecardSchool['latest.cost.tuition.in_state'] || null,
      website: scorecardSchool['school.school_url'] || null,
      student_size: scorecardSchool['latest.student.size'] || null,
      city: scorecardSchool['school.city'] || null,
      state: scorecardSchool['school.state'] || null,
      zip: scorecardSchool['school.zip'] || null,
      scorecard_id: scorecardSchool.id,
      programs: {
        bachelors: scorecardSchool['latest.academics.program_available.bachelors'] === true
      }
    },
    application_requirements: {
      essays: [],
      test_scores_required: ['SAT', 'ACT'],
      transcript_required: true,
      recommendation_letters: 2
    }
  };
}

// GET /api/test-scorecard - Test College Scorecard API integration
export const GET = async (request: NextRequest) => {
  return handleAsyncOperation(async () => {
    const url = new URL(request.url);
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '5'), 10);
    const state = url.searchParams.get('state') || 'CA';
    
    // Build API URL
    const params = new URLSearchParams({
      api_key: COLLEGE_SCORECARD_API_KEY,
      per_page: limit.toString(),
      page: '0',
      fields: [
        'id',
        'school.name',
        'school.city',
        'school.state',
        'school.zip',
        'school.school_url',
        'school.ownership',
        'latest.admissions.admission_rate.overall',
        'latest.cost.tuition.in_state',
        'latest.cost.tuition.out_of_state',
        'latest.student.size',
        'latest.academics.program_available.bachelors'
      ].join(',')
    });

    if (state) {
      params.append('school.state', state);
    }
    
    // Only use filters that are confirmed to work with the API
    params.append('school.operating', '1');

    const apiUrl = `${COLLEGE_SCORECARD_BASE_URL}?${params.toString()}`;
    
    console.log('Fetching from College Scorecard API:', apiUrl);

    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`College Scorecard API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const schools = data.results as CollegeScorecardSchool[];

    if (!schools || schools.length === 0) {
      return createStandardSuccessResponse(
        { 
          raw_data: data,
          transformed_schools: [],
          message: 'No schools found'
        },
        'No schools found matching criteria'
      );
    }

    // Transform schools
    const transformedSchools = schools
      .filter(school => school['school.name'] && school['school.name'].trim() !== '')
      .map(transformSchoolData);

    return createStandardSuccessResponse(
      {
        raw_data: data,
        transformed_schools: transformedSchools,
        api_url: apiUrl,
        total_found: schools.length,
        total_transformed: transformedSchools.length
      },
      `Successfully fetched and transformed ${transformedSchools.length} schools`
    );
  }, 'Failed to test College Scorecard API');
};

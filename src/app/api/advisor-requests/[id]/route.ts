import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation,
  parseRequestBody,
  AuthorizationError,
  NotFoundError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { User } from '@/types/application';

interface RouteContext {
  params: Promise<{ id: string }>;
}

// GET /api/advisor-requests/[id] - Get specific advisor request
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const params = await context.params;
      const { id } = params;

      // Students can only view their own requests, admins can view all
      let query = supabaseAdmin
        .from('advisor_requests')
        .select(`
          *,
          student_profiles!inner(
            id,
            users!inner(id, email, profile_data)
          ),
          consultants(
            id,
            users!inner(id, email, profile_data)
          )
        `)
        .eq('id', id);

      if (currentUser.role === 'student') {
        // Get student profile ID for current user
        const { data: studentProfile } = await supabaseAdmin
          .from('student_profiles')
          .select('id')
          .eq('user_id', currentUser.id)
          .single();

        if (!studentProfile) {
          throw new AuthorizationError('Student profile not found');
        }

        query = query.eq('student_id', studentProfile.id);
      } else if (currentUser.role !== 'admin') {
        throw new AuthorizationError('Only students and admins can view advisor requests');
      }

      const { data: advisorRequest, error } = await query.single();

      if (error || !advisorRequest) {
        throw new NotFoundError('Advisor request not found');
      }

      return createStandardSuccessResponse(
        advisorRequest,
        'Advisor request retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'read',
          resource_type: 'advisor_request',
          request_id: id
        }
      );
    }, 'Failed to retrieve advisor request');
  }
);

// PUT /api/advisor-requests/[id] - Update advisor request
export const PUT = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      const body = await parseRequestBody<{
        status?: 'pending' | 'approved' | 'rejected' | 'cancelled';
        admin_notes?: string;
        assigned_consultant_id?: string;
        message?: string;
        preferred_specialties?: string[];
      }>(request);

      // Get the existing request
      const { data: existingRequest, error: fetchError } = await supabaseAdmin
        .from('advisor_requests')
        .select('*')
        .eq('id', id)
        .single();

      if (fetchError || !existingRequest) {
        throw new NotFoundError('Advisor request not found');
      }

      // Authorization checks
      if (currentUser.role === 'student') {
        // Students can only update their own requests and only certain fields
        const { data: studentProfile } = await supabaseAdmin
          .from('student_profiles')
          .select('id')
          .eq('user_id', currentUser.id)
          .single();

        if (!studentProfile || studentProfile.id !== existingRequest.student_id) {
          throw new AuthorizationError('You can only update your own advisor requests');
        }

        // Students can only update message, preferred_specialties, or cancel
        if (body.status && body.status !== 'cancelled') {
          throw new AuthorizationError('Students can only cancel their requests');
        }

        if (body.admin_notes || body.assigned_consultant_id) {
          throw new AuthorizationError('Students cannot update admin fields');
        }
      } else if (currentUser.role !== 'admin') {
        throw new AuthorizationError('Only students and admins can update advisor requests');
      }

      // If approving request, create the consultant assignment
      if (body.status === 'approved' && body.assigned_consultant_id && currentUser.role === 'admin') {
        // Verify consultant exists
        const { data: consultant } = await supabaseAdmin
          .from('consultants')
          .select('id')
          .eq('id', body.assigned_consultant_id)
          .single();

        if (!consultant) {
          throw new Error('Assigned consultant not found');
        }

        // Create the student-consultant relationship
        const { error: assignmentError } = await supabaseAdmin
          .from('student_consultant_relations')
          .insert({
            student_id: existingRequest.student_id,
            consultant_id: body.assigned_consultant_id,
            start_date: new Date().toISOString().split('T')[0],
            status: 'active'
          });

        if (assignmentError) {
          throw new Error('Failed to create consultant assignment');
        }
      }

      const { data: advisorRequest, error } = await supabaseAdmin
        .from('advisor_requests')
        .update({
          ...body,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select(`
          *,
          student_profiles!inner(
            id,
            users!inner(id, email, profile_data)
          ),
          consultants(
            id,
            users!inner(id, email, profile_data)
          )
        `)
        .single();

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        advisorRequest,
        'Advisor request updated successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'update',
          resource_type: 'advisor_request',
          request_id: id,
          status_changed: !!body.status
        }
      );
    }, 'Failed to update advisor request');
  }
);

// DELETE /api/advisor-requests/[id] - Delete advisor request
export const DELETE = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      // Get the existing request
      const { data: existingRequest, error: fetchError } = await supabaseAdmin
        .from('advisor_requests')
        .select('*')
        .eq('id', id)
        .single();

      if (fetchError || !existingRequest) {
        throw new NotFoundError('Advisor request not found');
      }

      // Authorization checks
      if (currentUser.role === 'student') {
        const { data: studentProfile } = await supabaseAdmin
          .from('student_profiles')
          .select('id')
          .eq('user_id', currentUser.id)
          .single();

        if (!studentProfile || studentProfile.id !== existingRequest.student_id) {
          throw new AuthorizationError('You can only delete your own advisor requests');
        }
      } else if (currentUser.role !== 'admin') {
        throw new AuthorizationError('Only students and admins can delete advisor requests');
      }

      const { error } = await supabaseAdmin
        .from('advisor_requests')
        .delete()
        .eq('id', id);

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        null,
        'Advisor request deleted successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'delete',
          resource_type: 'advisor_request',
          request_id: id
        }
      );
    }, 'Failed to delete advisor request');
  }
);

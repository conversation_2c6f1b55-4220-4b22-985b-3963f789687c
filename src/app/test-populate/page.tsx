'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';

export default function TestPopulatePage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [limit, setLimit] = useState(5);
  const [state, setState] = useState('CA');

  const handlePopulate = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      const response = await fetch('/api/schools/populate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          limit,
          state: state || undefined
        })
      });

      const data = await response.json();
      setResult(data);
      
      if (data.success) {
        toast.success('Schools populated successfully!');
      } else {
        toast.error(`Error: ${data.error?.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Failed to populate schools');
      setResult({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setLoading(false);
    }
  };

  const handleGetInfo = async () => {
    setLoading(true);
    
    try {
      const response = await fetch('/api/schools/populate');
      const data = await response.json();
      setResult(data);
      
      if (data.success) {
        toast.success('Info retrieved successfully!');
      } else {
        toast.error(`Error: ${data.error?.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Failed to get info');
      setResult({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='container mx-auto p-8'>
      <Card>
        <CardHeader>
          <CardTitle>Test School Population</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-2 gap-4'>
            <div>
              <Label htmlFor='limit'>Limit</Label>
              <Input
                id='limit'
                type='number'
                value={limit}
                onChange={(e) => setLimit(parseInt(e.target.value) || 5)}
                min={1}
                max={100}
              />
            </div>
            <div>
              <Label htmlFor='state'>State (optional)</Label>
              <Input
                id='state'
                value={state}
                onChange={(e) => setState(e.target.value)}
                placeholder='e.g., CA, NY, TX'
              />
            </div>
          </div>
          
          <div className='flex gap-4'>
            <Button onClick={handlePopulate} disabled={loading}>
              {loading ? 'Populating...' : 'Populate Schools'}
            </Button>
            <Button onClick={handleGetInfo} disabled={loading} variant='outline'>
              Get Info
            </Button>
          </div>

          {result && (
            <Card>
              <CardHeader>
                <CardTitle>Result</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className='whitespace-pre-wrap text-sm bg-gray-100 p-4 rounded'>
                  {JSON.stringify(result, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

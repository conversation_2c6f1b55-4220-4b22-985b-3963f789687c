'use client';

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Icons } from '@/components/icons';
import { useCurrentUser } from '@/hooks/use-current-user';
import { toast } from 'sonner';

interface Invitation {
  id: string;
  code: string;
  email?: string;
  role: 'student' | 'consultant';
  expires_at: string;
  status: 'pending' | 'used' | 'expired';
  max_uses: number;
  current_uses: number;
  notes?: string;
  created_at: string;
  created_by_user?: {
    id: string;
    email: string;
    profile_data: any;
  };
  used_by_user?: {
    id: string;
    email: string;
    profile_data: any;
  };
}

export default function InvitationsPage() {
  const { user, loading } = useCurrentUser();
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [roleFilter, setRoleFilter] = useState<string>('all');

  // Modal states
  const [deleteDialog, setDeleteDialog] = useState<{open: boolean, invitation: Invitation | null}>({open: false, invitation: null});
  const [emailDialog, setEmailDialog] = useState<{open: boolean, invitation: Invitation | null}>({open: false, invitation: null});
  const [emailAddress, setEmailAddress] = useState('');

  // Create invitation form state
  const [createForm, setCreateForm] = useState({
    email: '',
    role: 'student' as 'student' | 'consultant',
    expires_in_days: 30,
    max_uses: 1,
    notes: ''
  });

  const fetchInvitations = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (roleFilter !== 'all') params.append('role', roleFilter);

      const response = await fetch(`/api/invitations?${params.toString()}`);
      const result = await response.json();

      if (result.success) {
        setInvitations(result.data);
      } else {
        toast.error('Failed to load invitations');
      }
    } catch (error) {
      console.error('Error fetching invitations:', error);
      toast.error('Failed to load invitations');
    } finally {
      setIsLoading(false);
    }
  }, [statusFilter, roleFilter]);

  const createInvitation = async () => {
    try {
      const response = await fetch('/api/invitations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: createForm.email || undefined,
          role: createForm.role,
          expires_in_days: createForm.expires_in_days,
          max_uses: createForm.max_uses,
          notes: createForm.notes || undefined
        })
      });

      const result = await response.json();

      if (result.success) {
        toast.success(`Invitation created! Code: ${result.meta.invitation_code}`);
        setIsCreateDialogOpen(false);
        setCreateForm({
          email: '',
          role: 'student',
          expires_in_days: 30,
          max_uses: 1,
          notes: ''
        });
        fetchInvitations();
      } else {
        toast.error(result.message || 'Failed to create invitation');
      }
    } catch (error) {
      console.error('Error creating invitation:', error);
      toast.error('Failed to create invitation');
    }
  };

  const openDeleteDialog = (invitation: Invitation) => {
    setDeleteDialog({open: true, invitation});
  };

  const confirmDeleteInvitation = async () => {
    if (!deleteDialog.invitation) return;

    try {
      const response = await fetch(`/api/invitations/${deleteDialog.invitation.id}`, {
        method: 'DELETE'
      });

      if (response.status === 404) {
        // Invitation already deleted or doesn't exist
        toast.success(`Invitation ${deleteDialog.invitation.code} was already cancelled`);
        fetchInvitations();
        setDeleteDialog({open: false, invitation: null});
        return;
      }

      const result = await response.json();

      if (result.success) {
        toast.success(`Invitation ${deleteDialog.invitation.code} cancelled successfully`);
        fetchInvitations();
        setDeleteDialog({open: false, invitation: null});
      } else {
        toast.error(result.message || 'Failed to cancel invitation');
      }
    } catch (error) {
      console.error('Error cancelling invitation:', error);
      toast.error('Failed to cancel invitation');
    }
  };

  const openEmailDialog = (invitation: Invitation) => {
    setEmailDialog({open: true, invitation});
    setEmailAddress(invitation.email || '');
  };

  const sendInvitationEmail = async () => {
    if (!emailDialog.invitation || !emailAddress) return;

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailAddress)) {
      toast.error('Please enter a valid email address');
      return;
    }

    try {
      const response = await fetch(`/api/invitations/${emailDialog.invitation.id}/send-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email: emailAddress })
      });

      const result = await response.json();

      if (result.success) {
        toast.success(`Invitation email sent to ${emailAddress}`);
        setEmailDialog({open: false, invitation: null});
        setEmailAddress('');
      } else {
        toast.error(result.message || 'Failed to send invitation email');
      }
    } catch (error) {
      console.error('Error sending invitation email:', error);
      toast.error('Failed to send invitation email');
    }
  };

  const copyInvitationCode = (code: string) => {
    navigator.clipboard.writeText(code);
    toast.success('Invitation code copied to clipboard');
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-blue-600">Pending</Badge>;
      case 'used':
        return <Badge variant="outline" className="text-green-600">Used</Badge>;
      case 'expired':
        return <Badge variant="outline" className="text-red-600">Expired</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getRoleBadge = (role: string) => {
    return (
      <Badge variant={role === 'student' ? 'default' : 'secondary'}>
        {role === 'student' ? 'Student' : 'Consultant'}
      </Badge>
    );
  };

  const getCreatorName = (invitation: Invitation) => {
    const user = invitation.created_by_user;
    if (!user) return 'Unknown';

    // Try firstName + lastName first
    if (user.profile_data?.firstName && user.profile_data?.lastName) {
      return `${user.profile_data.firstName} ${user.profile_data.lastName}`;
    }

    // Try name field
    if (user.profile_data?.name) {
      return user.profile_data.name;
    }

    // Fallback to email username
    if (user.email) {
      return user.email.split('@')[0];
    }

    return 'Unknown';
  };

  useEffect(() => {
    if (user && user.role === 'admin') {
      fetchInvitations();
    }
  }, [user, statusFilter, roleFilter, fetchInvitations]);

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading...</div>;
  }

  if (!user || user.role !== 'admin') {
    return <div className="flex items-center justify-center h-64">Access denied</div>;
  }

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Invitations</h1>
          <p className="text-muted-foreground">
            Manage invitation codes and registration access
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Icons.plus className="mr-2 h-4 w-4" />
              Create Invitation
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Invitation</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">

              <div>
                <Label htmlFor="email">Email (Optional)</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Leave empty for general invitation"
                  value={createForm.email}
                  onChange={(e) => setCreateForm({ ...createForm, email: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="role">Role</Label>
                <Select value={createForm.role} onValueChange={(value: 'student' | 'consultant') => setCreateForm({ ...createForm, role: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="student">Student</SelectItem>
                    <SelectItem value="consultant">Consultant</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="expires_in_days">Expires in (days)</Label>
                <Input
                  id="expires_in_days"
                  type="number"
                  min="1"
                  max="365"
                  value={createForm.expires_in_days}
                  onChange={(e) => setCreateForm({ ...createForm, expires_in_days: parseInt(e.target.value) || 30 })}
                />
              </div>
              <div>
                <Label htmlFor="max_uses">Maximum Uses</Label>
                <Input
                  id="max_uses"
                  type="number"
                  min="1"
                  max="100"
                  value={createForm.max_uses}
                  onChange={(e) => setCreateForm({ ...createForm, max_uses: parseInt(e.target.value) || 1 })}
                />
              </div>
              <div>
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  placeholder="Internal notes about this invitation"
                  value={createForm.notes}
                  onChange={(e) => setCreateForm({ ...createForm, notes: e.target.value })}
                />
              </div>
              <Button onClick={createInvitation} className="w-full">
                Create Invitation
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="used">Used</SelectItem>
            <SelectItem value="expired">Expired</SelectItem>
          </SelectContent>
        </Select>
        <Select value={roleFilter} onValueChange={setRoleFilter}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Filter by role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Roles</SelectItem>
            <SelectItem value="student">Student</SelectItem>
            <SelectItem value="consultant">Consultant</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Invitations List */}
      <div className="grid gap-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <Icons.spinner className="h-6 w-6 animate-spin" />
          </div>
        ) : invitations.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Icons.mail className="text-muted-foreground mb-4 h-12 w-12" />
              <h3 className="mb-2 text-lg font-semibold">No invitations found</h3>
              <p className="text-muted-foreground mb-4 text-center">
                Create your first invitation to start managing user registration
              </p>
            </CardContent>
          </Card>
        ) : (
          invitations.map((invitation) => (
            <Card key={invitation.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-lg font-mono">{invitation.code}</CardTitle>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyInvitationCode(invitation.code)}
                      title="Copy invitation code"
                    >
                      <Icons.copy className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openEmailDialog(invitation)}
                      title="Send invitation email"
                      disabled={invitation.status !== 'pending'}
                    >
                      <Icons.mail className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openDeleteDialog(invitation)}
                      className="text-destructive hover:text-destructive"
                      title="Cancel invitation"
                    >
                      <Icons.trash className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(invitation.status)}
                    {getRoleBadge(invitation.role)}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Email</p>
                    <p>{invitation.email || 'General invitation'}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Usage</p>
                    <p>{invitation.current_uses} / {invitation.max_uses}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Expires</p>
                    <p>{new Date(invitation.expires_at).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Created</p>
                    <p>{new Date(invitation.created_at).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Created By</p>
                    <p>{getCreatorName(invitation)}</p>
                  </div>
                </div>
                {invitation.notes && (
                  <div className="mt-4">
                    <p className="text-muted-foreground text-sm">Notes</p>
                    <p className="text-sm">{invitation.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.open} onOpenChange={(open) => setDeleteDialog({open, invitation: null})}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Invitation</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel invitation {deleteDialog.invitation?.code}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialog({open: false, invitation: null})}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteInvitation}
            >
              Cancel Invitation
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Email Dialog */}
      <Dialog open={emailDialog.open} onOpenChange={(open) => setEmailDialog({open, invitation: null})}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Send Invitation Email</DialogTitle>
            <DialogDescription>
              Send invitation {emailDialog.invitation?.code} to an email address.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="emailAddress">Email Address</Label>
              <Input
                id="emailAddress"
                type="email"
                placeholder="Enter email address"
                value={emailAddress}
                onChange={(e) => setEmailAddress(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setEmailDialog({open: false, invitation: null});
                setEmailAddress('');
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={sendInvitationEmail}
              disabled={!emailAddress}
            >
              Send Email
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

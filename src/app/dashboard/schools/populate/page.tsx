'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Icons } from '@/components/icons';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useRouter } from 'next/navigation';
import { useApiGet, useApiPost } from '@/lib/api-client';

interface PopulateStats {
  current_school_count: number;
  available_states: string[];
  api_limits: {
    max_per_request: number;
    recommended_per_request: number;
  };
  usage_tips: string[];
}

interface PopulateResult {
  imported: number;
  skipped: number;
  errors: number;
  total_processed: number;
  error_details?: string[];
}

export default function PopulateSchoolsPage() {
  const { user, loading: userLoading, isAdmin } = useCurrentUser();
  const router = useRouter();
  const [selectedState, setSelectedState] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [limit, setLimit] = useState(50);
  const [isPopulating, setIsPopulating] = useState(false);
  const [result, setResult] = useState<PopulateResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch population stats
  const {
    data: stats,
    loading: statsLoading,
    error: statsError
  } = useApiGet<PopulateStats>('/api/schools/populate');

  const { execute: populateSchools } = useApiPost<PopulateResult>('/api/schools/populate');

  // Redirect non-admin users
  if (!userLoading && !isAdmin) {
    router.push('/dashboard');
    return null;
  }

  if (userLoading || statsLoading) {
    return (
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-center py-12">
          <Icons.spinner className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (statsError) {
    return (
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <Alert variant="destructive">
          <Icons.alertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load population stats: {statsError}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const handlePopulate = async () => {
    setIsPopulating(true);
    setError(null);
    setResult(null);

    try {
      const response = await populateSchools({
        state: selectedState || undefined,
        search: searchTerm || undefined,
        limit,
        page: 0
      });

      if (response.success) {
        setResult(response.data);
      } else {
        setError(response.error || 'Failed to populate schools');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsPopulating(false);
    }
  };

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Populate Schools</h2>
          <p className="text-muted-foreground">
            Import school data from the College Scorecard API
          </p>
        </div>
        <Button variant="outline" onClick={() => router.push('/dashboard/schools')}>
          <Icons.arrowLeft className="mr-2 h-4 w-4" />
          Back to Schools
        </Button>
      </div>

      {/* Current Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Current Database Status</CardTitle>
          <CardDescription>
            Overview of schools currently in the database
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label>Schools in Database</Label>
              <div className="text-2xl font-bold">{stats?.current_school_count || 0}</div>
            </div>
            <div className="space-y-2">
              <Label>API Limits</Label>
              <div className="text-sm text-muted-foreground">
                Max per request: {stats?.api_limits.max_per_request}<br />
                Recommended: {stats?.api_limits.recommended_per_request}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Population Form */}
      <Card>
        <CardHeader>
          <CardTitle>Import Schools</CardTitle>
          <CardDescription>
            Configure and run school data import from College Scorecard
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="state">Filter by State (Optional)</Label>
              <Select value={selectedState} onValueChange={setSelectedState}>
                <SelectTrigger>
                  <SelectValue placeholder="All states" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All states</SelectItem>
                  {stats?.available_states.map((state) => (
                    <SelectItem key={state} value={state}>
                      {state}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="search">Search Term (Optional)</Label>
              <Input
                id="search"
                placeholder="e.g., University, College"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="limit">Number of Schools to Import</Label>
            <Input
              id="limit"
              type="number"
              min="1"
              max={stats?.api_limits.max_per_request || 500}
              value={limit}
              onChange={(e) => setLimit(parseInt(e.target.value) || 50)}
            />
            <p className="text-sm text-muted-foreground">
              Recommended: Start with 50-100 schools for testing
            </p>
          </div>

          <Button 
            onClick={handlePopulate} 
            disabled={isPopulating}
            className="w-full"
          >
            {isPopulating ? (
              <>
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                Importing Schools...
              </>
            ) : (
              <>
                <Icons.download className="mr-2 h-4 w-4" />
                Import Schools
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Usage Tips */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Tips</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2 text-sm">
            {stats?.usage_tips.map((tip, index) => (
              <li key={index} className="flex items-start gap-2">
                <Icons.lightbulb className="h-4 w-4 mt-0.5 text-yellow-500" />
                {tip}
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>

      {/* Results */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icons.checkCircle className="h-5 w-5 text-green-500" />
              Import Complete
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{result.imported}</div>
                <div className="text-sm text-muted-foreground">Imported</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{result.skipped}</div>
                <div className="text-sm text-muted-foreground">Skipped</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{result.errors}</div>
                <div className="text-sm text-muted-foreground">Errors</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{result.total_processed}</div>
                <div className="text-sm text-muted-foreground">Total Processed</div>
              </div>
            </div>

            {result.error_details && result.error_details.length > 0 && (
              <div className="mt-4">
                <Label>Error Details (First 10)</Label>
                <div className="mt-2 space-y-1">
                  {result.error_details.map((error, index) => (
                    <div key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded">
                      {error}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <Icons.alertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}

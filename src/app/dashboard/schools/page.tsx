'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { SchoolSearch, SchoolSelectorDialog, School } from '@/components/schools';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { School as SchoolIcon, Target, Heart, BookOpen } from 'lucide-react';
import { useCurrentUser } from '@/hooks/use-current-user';

interface DatabaseStats {
  totalSchools: number;
  publicSchools: number;
  privateSchools: number;
  avgAdmissionRate: number;
}

// New Schools Management Component
function SchoolsManagement() {
  const [targetSchools, setTargetSchools] = useState<School[]>([]);
  const [safetySchools, setSafetySchools] = useState<School[]>([]);
  const [reachSchools, setReachSchools] = useState<School[]>([]);
  const [favoriteSchools, setFavoriteSchools] = useState<School[]>([]);
  const [databaseStats, setDatabaseStats] = useState<DatabaseStats>({
    totalSchools: 0,
    publicSchools: 0,
    privateSchools: 0,
    avgAdmissionRate: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDatabaseStats = async () => {
      try {
        const response = await fetch('/api/schools/search?limit=1');
        const data = await response.json();

        if (data.success) {
          // Get total count from the response
          const totalSchools = data.data.total || 0;

          // Fetch some sample data to calculate stats
          const statsResponse = await fetch('/api/schools/search?limit=100');
          const statsData = await statsResponse.json();

          if (statsData.success && statsData.data.schools) {
            const schools = statsData.data.schools;
            const publicCount = schools.filter((s: School) => s.ownership === 1).length;
            const privateCount = schools.filter((s: School) => s.ownership !== 1).length;
            const schoolsWithAdmission = schools.filter((s: School) => s.admission_rate_percent);
            const avgAdmission = schoolsWithAdmission.length > 0
              ? Math.round(schoolsWithAdmission.reduce((sum: number, s: School) => sum + (s.admission_rate_percent || 0), 0) / schoolsWithAdmission.length)
              : 0;

            setDatabaseStats({
              totalSchools,
              publicSchools: Math.round((publicCount / schools.length) * totalSchools),
              privateSchools: Math.round((privateCount / schools.length) * totalSchools),
              avgAdmissionRate: avgAdmission
            });
          }
        }
      } catch (error) {
        console.error('Failed to fetch database stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDatabaseStats();
  }, []);

  const allSelectedSchools = [
    ...targetSchools,
    ...safetySchools,
    ...reachSchools,
    ...favoriteSchools
  ];

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">School Selection</h2>
          <p className="text-muted-foreground">
            Search and organize your target schools from our database of {databaseStats.totalSchools.toLocaleString()}+ institutions
          </p>
        </div>
      </div>

      {/* Database Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Schools</CardTitle>
            <SchoolIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : databaseStats.totalSchools.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              In our database
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Public Schools</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : databaseStats.publicSchools.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Public institutions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Private Schools</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : databaseStats.privateSchools.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Private institutions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Admission Rate</CardTitle>
            <Heart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : `${databaseStats.avgAdmissionRate}%`}
            </div>
            <p className="text-xs text-muted-foreground">
              Average across schools
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="search" className="space-y-4">
        <TabsList>
          <TabsTrigger value="search">Search Schools</TabsTrigger>
          <TabsTrigger value="organize">Organize Schools</TabsTrigger>
          <TabsTrigger value="overview">My Schools ({allSelectedSchools.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="search" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>School Search</CardTitle>
              <p className="text-sm text-muted-foreground">
                Search through our database of {databaseStats.totalSchools.toLocaleString()}+ schools. Click on any school to view details.
              </p>
            </CardHeader>
            <CardContent>
              <SchoolSearch
                enableNavigation={true}
                placeholder="Search for colleges and universities..."
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="organize" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-blue-500" />
                  Target Schools ({targetSchools.length})
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Schools that match your academic profile well
                </p>
              </CardHeader>
              <CardContent>
                <SchoolSelectorDialog
                  selectedSchools={targetSchools}
                  onSchoolsChange={setTargetSchools}
                  triggerText="Add Target Schools"
                  title="Select Target Schools"
                  description="Choose schools that align with your academic credentials"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SchoolIcon className="h-5 w-5 text-green-500" />
                  Safety Schools ({safetySchools.length})
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Schools where you're likely to be admitted
                </p>
              </CardHeader>
              <CardContent>
                <SchoolSelectorDialog
                  selectedSchools={safetySchools}
                  onSchoolsChange={setSafetySchools}
                  triggerText="Add Safety Schools"
                  title="Select Safety Schools"
                  description="Choose schools where you have a strong chance of admission"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5 text-red-500" />
                  Reach Schools ({reachSchools.length})
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Ambitious choices that would be great to get into
                </p>
              </CardHeader>
              <CardContent>
                <SchoolSelectorDialog
                  selectedSchools={reachSchools}
                  onSchoolsChange={setReachSchools}
                  triggerText="Add Reach Schools"
                  title="Select Reach Schools"
                  description="Choose schools that are ambitious but achievable goals"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5 text-purple-500" />
                  Favorite Schools ({favoriteSchools.length})
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Schools you're particularly interested in
                </p>
              </CardHeader>
              <CardContent>
                <SchoolSelectorDialog
                  selectedSchools={favoriteSchools}
                  onSchoolsChange={setFavoriteSchools}
                  triggerText="Add Favorite Schools"
                  title="Select Favorite Schools"
                  description="Choose schools that particularly interest you"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="overview" className="space-y-4">
          {allSelectedSchools.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <SchoolIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No schools selected yet</h3>
                <p className="text-muted-foreground mb-4">
                  Start by searching for schools and organizing them into categories.
                </p>
                <Button onClick={() => document.querySelector('[value="search"]')?.click()}>
                  Search Schools
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {[
                { title: 'Target Schools', schools: targetSchools, color: 'blue' },
                { title: 'Safety Schools', schools: safetySchools, color: 'green' },
                { title: 'Reach Schools', schools: reachSchools, color: 'red' },
                { title: 'Favorite Schools', schools: favoriteSchools, color: 'purple' }
              ].filter(({ schools }) => schools.length > 0).map(({ title, schools, color }) => (
                <Card key={title}>
                  <CardHeader>
                    <CardTitle>{title} ({schools.length})</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {schools.map((school) => (
                        <div key={school.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <h4 className="font-medium">{school.name}</h4>
                            <p className="text-sm text-muted-foreground">
                              {school.city}, {school.state} • {school.ownership_label}
                            </p>
                          </div>
                          <div className="flex gap-2">
                            {school.admission_rate_percent && (
                              <Badge variant="outline">
                                {school.admission_rate_percent}% admission
                              </Badge>
                            )}
                            {school.student_size && (
                              <Badge variant="secondary">
                                {school.student_size.toLocaleString()} students
                              </Badge>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default function SchoolsPage() {
  return <SchoolsManagement />;
}
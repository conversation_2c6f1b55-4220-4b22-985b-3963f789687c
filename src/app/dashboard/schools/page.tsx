'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Icons } from '@/components/icons';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useApiGet } from '@/lib/api-client';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

interface School {
  id: string;
  name: string;
  details?: {
    location?: string;
    type?: string;
    admissionRate?: number;
    website?: string;
    description?: string;
  };
  application_requirements?: {
    essays?: number;
    recommendations?: number;
    transcripts?: boolean;
    testScores?: boolean;
  };
  created_at: string;
  updated_at: string;
}

interface SchoolStats {
  totalSchools: number;
  publicSchools: number;
  privateSchools: number;
  averageAdmissionRate: number;
  schoolsByState: Record<string, number>;
  applicationStats: {
    totalApplications: number;
    activeApplications: number;
    submittedApplications: number;
  };
}

// Admin Schools Management Component
function AdminSchoolsManagement() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');

  const {
    data: schoolsResponse,
    loading: schoolsLoading,
    error: schoolsError,
    fetch: fetchSchools
  } = useApiGet<School[]>('/api/schools');

  const {
    data: statsResponse,
    loading: statsLoading,
    error: statsError,
    fetch: fetchStats
  } = useApiGet<SchoolStats>('/api/schools/stats');

  useEffect(() => {
    fetchSchools();
    fetchStats();
  }, [fetchSchools, fetchStats]);

  const schools = schoolsResponse || [];
  const stats = statsResponse || {
    totalSchools: 0,
    publicSchools: 0,
    privateSchools: 0,
    averageAdmissionRate: 0,
    schoolsByState: {},
    applicationStats: {
      totalApplications: 0,
      activeApplications: 0,
      submittedApplications: 0
    }
  };

  // Filter schools based on search and type
  const filteredSchools = schools.filter((school) => {
    const matchesSearch = school.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      school.details?.location?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = typeFilter === 'all' ||
      school.details?.type?.toLowerCase() === typeFilter.toLowerCase();

    return matchesSearch && matchesType;
  });

  const loading = schoolsLoading || statsLoading;

  // Handler for Add School button
  const handleAddSchool = () => {
    // For admin, we need to create a proper admin school creation page
    // For now, redirect to the existing add school page
    router.push('/dashboard/schools/add');
  };

  // Handler for Populate Schools button
  const handlePopulateSchools = () => {
    router.push('/dashboard/schools/populate');
  };

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      {/* Header */}
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>Schools Management</h2>
          <p className='text-muted-foreground'>
            Manage all schools in the system and track application statistics
          </p>
        </div>
        <div className='flex gap-2'>
          <Button
            variant='outline'
            onClick={() => setViewMode(viewMode === 'table' ? 'cards' : 'table')}
          >
            {viewMode === 'table' ? (
              <Icons.grid className='mr-2 h-4 w-4' />
            ) : (
              <Icons.list className='mr-2 h-4 w-4' />
            )}
            {viewMode === 'table' ? 'Card View' : 'Table View'}
          </Button>
          <Button variant="outline" onClick={handlePopulateSchools}>
            <Icons.download className='mr-2 h-4 w-4' />
            Populate Schools
          </Button>
          <Button onClick={handleAddSchool}>
            <Icons.add className='mr-2 h-4 w-4' />
            Add School
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Schools</CardTitle>
            <Icons.building className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.totalSchools}</div>
            <p className='text-muted-foreground text-xs'>In the database</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Public Schools</CardTitle>
            <Icons.building className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.publicSchools}</div>
            <p className='text-muted-foreground text-xs'>
              {stats.totalSchools > 0 ? Math.round((stats.publicSchools / stats.totalSchools) * 100) : 0}% of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Private Schools</CardTitle>
            <Icons.building className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.privateSchools}</div>
            <p className='text-muted-foreground text-xs'>
              {stats.totalSchools > 0 ? Math.round((stats.privateSchools / stats.totalSchools) * 100) : 0}% of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Active Applications</CardTitle>
            <Icons.fileText className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.applicationStats.activeApplications}</div>
            <p className='text-muted-foreground text-xs'>Currently in progress</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className='flex flex-col gap-4 md:flex-row md:items-center md:justify-between'>
        <div className='flex flex-1 gap-4'>
          <div className='flex-1'>
            <Input
              placeholder='Search schools by name or location...'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className='max-w-sm'
            />
          </div>
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className='w-[180px]'>
              <SelectValue placeholder='Filter by type' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Types</SelectItem>
              <SelectItem value='public'>Public</SelectItem>
              <SelectItem value='private'>Private</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className='text-muted-foreground text-sm'>
          Showing {filteredSchools.length} of {schools.length} schools
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <Card>
          <CardContent className='flex items-center justify-center py-12'>
            <div className='flex items-center gap-2'>
              <Icons.spinner className='h-4 w-4 animate-spin' />
              <span>Loading schools...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {(schoolsError || statsError) && (
        <Card>
          <CardContent className='flex items-center justify-center py-12'>
            <div className='text-center'>
              <Icons.alertCircle className='text-destructive mx-auto mb-4 h-12 w-12' />
              <h3 className='mb-2 text-lg font-semibold'>Error Loading Schools</h3>
              <p className='text-muted-foreground mb-4'>
                {schoolsError || statsError || 'Failed to load schools data'}
              </p>
              <Button onClick={() => { fetchSchools(); fetchStats(); }}>
                <Icons.refresh className='mr-2 h-4 w-4' />
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Schools Display */}
      {!loading && !schoolsError && !statsError && (
        <>
          {viewMode === 'table' ? (
            <Card>
              <CardContent className='p-0'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>School</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Admission Rate</TableHead>
                      <TableHead>Applications</TableHead>
                      <TableHead className='w-[70px]'>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSchools.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className='py-12 text-center'>
                          <div className='flex flex-col items-center'>
                            <Icons.building className='text-muted-foreground mb-4 h-12 w-12' />
                            <h3 className='mb-2 text-lg font-semibold'>No schools found</h3>
                            <p className='text-muted-foreground'>
                              {searchTerm || typeFilter !== 'all'
                                ? 'Try adjusting your search or filters'
                                : 'No schools available'}
                            </p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredSchools.map((school) => (
                        <TableRow key={school.id}>
                          <TableCell>
                            <div>
                              <div className='font-medium'>{school.name}</div>
                              <div className='text-muted-foreground text-sm'>
                                {school.details?.website || 'No website'}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant='outline'>
                              {school.details?.type || 'Unknown'}
                            </Badge>
                          </TableCell>
                          <TableCell>{school.details?.location || 'Not specified'}</TableCell>
                          <TableCell>
                            {school.details?.admissionRate
                              ? `${school.details.admissionRate}%`
                              : 'N/A'}
                          </TableCell>
                          <TableCell>
                            <div className='text-sm'>
                              <div>Active: 0</div>
                              <div className='text-muted-foreground'>Total: 0</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant='ghost' size='sm'>
                                  <Icons.moreHorizontal className='h-4 w-4' />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align='end'>
                                <DropdownMenuItem>
                                  <Icons.eye className='mr-2 h-4 w-4' />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Icons.edit className='mr-2 h-4 w-4' />
                                  Edit School
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Icons.fileText className='mr-2 h-4 w-4' />
                                  View Applications
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : (
            /* Card View */
            <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
              {filteredSchools.length === 0 ? (
                <Card className='md:col-span-2 lg:col-span-3'>
                  <CardContent className='flex flex-col items-center justify-center py-12'>
                    <Icons.building className='text-muted-foreground mb-4 h-12 w-12' />
                    <h3 className='mb-2 text-lg font-semibold'>No schools found</h3>
                    <p className='text-muted-foreground mb-4 text-center'>
                      {searchTerm || typeFilter !== 'all'
                        ? 'Try adjusting your search or filters'
                        : 'No schools available'}
                    </p>
                    <div className="flex gap-2">
                      <Button variant="outline" onClick={handlePopulateSchools}>
                        <Icons.download className='mr-2 h-4 w-4' />
                        Populate Schools
                      </Button>
                      <Button onClick={handleAddSchool}>
                        <Icons.add className='mr-2 h-4 w-4' />
                        Add School
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                filteredSchools.map((school) => (
                  <Card key={school.id} className='transition-shadow hover:shadow-md'>
                    <CardContent className='p-6'>
                      <div className='mb-4 flex items-start justify-between'>
                        <div>
                          <h3 className='font-semibold'>{school.name}</h3>
                          <p className='text-muted-foreground text-sm'>
                            {school.details?.location || 'Location not specified'}
                          </p>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant='ghost' size='sm'>
                              <Icons.moreHorizontal className='h-4 w-4' />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align='end'>
                            <DropdownMenuItem>
                              <Icons.eye className='mr-2 h-4 w-4' />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Icons.edit className='mr-2 h-4 w-4' />
                              Edit School
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Icons.fileText className='mr-2 h-4 w-4' />
                              View Applications
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <div className='space-y-2'>
                        <div className='flex items-center justify-between text-sm'>
                          <span>Type:</span>
                          <Badge variant='outline'>
                            {school.details?.type || 'Unknown'}
                          </Badge>
                        </div>
                        <div className='flex items-center justify-between text-sm'>
                          <span>Admission Rate:</span>
                          <span className='font-medium'>
                            {school.details?.admissionRate
                              ? `${school.details.admissionRate}%`
                              : 'N/A'}
                          </span>
                        </div>
                        <div className='flex items-center justify-between text-sm'>
                          <span>Active Applications:</span>
                          <span className='font-medium'>0</span>
                        </div>
                      </div>

                      <div className='mt-4 flex gap-2'>
                        <Button size='sm' variant='outline' className='flex-1'>
                          <Icons.eye className='mr-2 h-4 w-4' />
                          View
                        </Button>
                        <Button size='sm' className='flex-1'>
                          <Icons.edit className='mr-2 h-4 w-4' />
                          Edit
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
}

// Student Schools Component (existing functionality)
function StudentSchoolsManagement() {
  // Mock data - in real app, this would come from API
  const targetSchools = [
    {
      id: '1',
      name: 'Harvard University',
      location: 'Cambridge, MA',
      type: 'Private',
      admissionRate: 3.4,
      applicationStatus: 'submitted',
      applicationDeadline: '2024-11-01',
      applicationRound: 'Early Action',
      requirements: {
        essays: { completed: 3, total: 3 },
        recommendations: { completed: 3, total: 3 },
        transcripts: { completed: 1, total: 1 },
        testScores: { completed: 1, total: 1 }
      },
      notes: 'Strong fit for pre-med program'
    },
    {
      id: '2',
      name: 'Stanford University',
      location: 'Stanford, CA',
      type: 'Private',
      admissionRate: 3.9,
      applicationStatus: 'in_progress',
      applicationDeadline: '2025-01-05',
      applicationRound: 'Regular Decision',
      requirements: {
        essays: { completed: 2, total: 4 },
        recommendations: { completed: 3, total: 3 },
        transcripts: { completed: 1, total: 1 },
        testScores: { completed: 1, total: 1 }
      },
      notes: 'Excellent engineering program'
    },
    {
      id: '3',
      name: 'MIT',
      location: 'Cambridge, MA',
      type: 'Private',
      admissionRate: 4.1,
      applicationStatus: 'not_started',
      applicationDeadline: '2025-01-01',
      applicationRound: 'Regular Decision',
      requirements: {
        essays: { completed: 0, total: 5 },
        recommendations: { completed: 2, total: 3 },
        transcripts: { completed: 1, total: 1 },
        testScores: { completed: 1, total: 1 }
      },
      notes: 'Reach school - strong STEM focus'
    },
    {
      id: '4',
      name: 'University of California, Berkeley',
      location: 'Berkeley, CA',
      type: 'Public',
      admissionRate: 14.5,
      applicationStatus: 'completed',
      applicationDeadline: '2024-11-30',
      applicationRound: 'Regular Decision',
      requirements: {
        essays: { completed: 4, total: 4 },
        recommendations: { completed: 0, total: 0 },
        transcripts: { completed: 1, total: 1 },
        testScores: { completed: 1, total: 1 }
      },
      notes: 'Target school - great value'
    },
    {
      id: '5',
      name: 'University of Michigan',
      location: 'Ann Arbor, MI',
      type: 'Public',
      admissionRate: 20.2,
      applicationStatus: 'in_progress',
      applicationDeadline: '2025-02-01',
      applicationRound: 'Regular Decision',
      requirements: {
        essays: { completed: 1, total: 2 },
        recommendations: { completed: 2, total: 2 },
        transcripts: { completed: 1, total: 1 },
        testScores: { completed: 1, total: 1 }
      },
      notes: 'Safety school with strong programs'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'submitted':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'not_started':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'submitted':
        return 'Submitted';
      case 'in_progress':
        return 'In Progress';
      case 'not_started':
        return 'Not Started';
      default:
        return 'Unknown';
    }
  };

  const calculateProgress = (requirements: any) => {
    const totalCompleted = Object.values(requirements).reduce(
      (sum: number, req: any) => sum + req.completed,
      0
    );
    const totalRequired = Object.values(requirements).reduce(
      (sum: number, req: any) => sum + req.total,
      0
    );
    return totalRequired > 0 ? (totalCompleted / totalRequired) * 100 : 0;
  };

  const getDaysUntilDeadline = (deadline: string) => {
    const deadlineDate = new Date(deadline);
    const today = new Date();
    const diffTime = deadlineDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>Target Schools</h2>
          <p className='text-muted-foreground'>
            Manage your college applications and track progress
          </p>
        </div>
        <Button asChild>
          <Link href='/dashboard/schools/add'>
            <Icons.add className='mr-2 h-4 w-4' />
            Add School
          </Link>
        </Button>
      </div>

      {/* Overview Stats */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Schools</CardTitle>
            <Icons.building className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{targetSchools.length}</div>
            <p className='text-muted-foreground text-xs'>In your target list</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Applications Submitted
            </CardTitle>
            <Icons.checkCircle className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {
                targetSchools.filter(
                  (s) =>
                    s.applicationStatus === 'submitted' ||
                    s.applicationStatus === 'completed'
                ).length
              }
            </div>
            <p className='text-muted-foreground text-xs'>
              Out of {targetSchools.length} schools
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Avg. Admission Rate
            </CardTitle>
            <Icons.target className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {(
                targetSchools.reduce(
                  (sum, school) => sum + school.admissionRate,
                  0
                ) / targetSchools.length
              ).toFixed(1)}
              %
            </div>
            <p className='text-muted-foreground text-xs'>Across all schools</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Upcoming Deadlines
            </CardTitle>
            <Icons.calendar className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {
                targetSchools.filter(
                  (s) =>
                    getDaysUntilDeadline(s.applicationDeadline) <= 30 &&
                    getDaysUntilDeadline(s.applicationDeadline) > 0
                ).length
              }
            </div>
            <p className='text-muted-foreground text-xs'>In next 30 days</p>
          </CardContent>
        </Card>
      </div>

      {/* Schools List */}
      <div className='space-y-4'>
        {targetSchools.map((school) => {
          const progress = calculateProgress(school.requirements);
          const daysUntilDeadline = getDaysUntilDeadline(
            school.applicationDeadline
          );

          return (
            <Card key={school.id}>
              <CardHeader>
                <div className='flex items-start justify-between'>
                  <div className='space-y-2'>
                    <div className='flex items-center space-x-2'>
                      <CardTitle className='text-lg'>{school.name}</CardTitle>
                      <Badge variant='outline'>{school.type}</Badge>
                      <Badge
                        className={getStatusColor(school.applicationStatus)}
                      >
                        {getStatusText(school.applicationStatus)}
                      </Badge>
                    </div>
                    <CardDescription>
                      {school.location} • {school.admissionRate}% admission rate
                      • {school.applicationRound}
                    </CardDescription>
                  </div>
                  <Button asChild size='sm'>
                    <Link href={`/dashboard/schools/${school.id}`}>
                      View Details
                    </Link>
                  </Button>
                </div>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <span className='text-sm font-medium'>
                    Application Progress
                  </span>
                  <span className='text-muted-foreground text-sm'>
                    {Math.round(progress)}% complete
                  </span>
                </div>
                <Progress value={progress} className='h-2' />

                <div className='grid grid-cols-2 gap-4 text-sm md:grid-cols-4'>
                  <div className='flex items-center justify-between'>
                    <span>Essays</span>
                    <span className='font-medium'>
                      {school.requirements.essays.completed}/
                      {school.requirements.essays.total}
                    </span>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span>Recommendations</span>
                    <span className='font-medium'>
                      {school.requirements.recommendations.completed}/
                      {school.requirements.recommendations.total}
                    </span>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span>Transcripts</span>
                    <span className='font-medium'>
                      {school.requirements.transcripts.completed}/
                      {school.requirements.transcripts.total}
                    </span>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span>Test Scores</span>
                    <span className='font-medium'>
                      {school.requirements.testScores.completed}/
                      {school.requirements.testScores.total}
                    </span>
                  </div>
                </div>

                <div className='flex items-center justify-between border-t pt-4'>
                  <div className='flex items-center space-x-4'>
                    <div className='flex items-center space-x-2'>
                      <Icons.calendar className='text-muted-foreground h-4 w-4' />
                      <span className='text-sm'>
                        Deadline:{' '}
                        {new Date(
                          school.applicationDeadline
                        ).toLocaleDateString()}
                      </span>
                    </div>
                    {daysUntilDeadline > 0 && (
                      <Badge
                        variant={
                          daysUntilDeadline <= 7
                            ? 'destructive'
                            : daysUntilDeadline <= 30
                              ? 'secondary'
                              : 'outline'
                        }
                      >
                        {daysUntilDeadline} days left
                      </Badge>
                    )}
                  </div>
                  <div className='text-muted-foreground text-sm'>
                    {school.notes}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* School Categories */}
      <div className='grid gap-4 md:grid-cols-3'>
        <Card>
          <CardHeader>
            <CardTitle className='text-lg'>Reach Schools</CardTitle>
            <CardDescription>Admission rate &lt; 10%</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-2'>
              {targetSchools
                .filter((s) => s.admissionRate < 10)
                .map((school) => (
                  <div
                    key={school.id}
                    className='flex items-center justify-between text-sm'
                  >
                    <span>{school.name}</span>
                    <span className='text-muted-foreground'>
                      {school.admissionRate}%
                    </span>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='text-lg'>Target Schools</CardTitle>
            <CardDescription>Admission rate 10-25%</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-2'>
              {targetSchools
                .filter((s) => s.admissionRate >= 10 && s.admissionRate <= 25)
                .map((school) => (
                  <div
                    key={school.id}
                    className='flex items-center justify-between text-sm'
                  >
                    <span>{school.name}</span>
                    <span className='text-muted-foreground'>
                      {school.admissionRate}%
                    </span>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='text-lg'>Safety Schools</CardTitle>
            <CardDescription>Admission rate &gt; 25%</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-2'>
              {targetSchools
                .filter((s) => s.admissionRate > 25)
                .map((school) => (
                  <div
                    key={school.id}
                    className='flex items-center justify-between text-sm'
                  >
                    <span>{school.name}</span>
                    <span className='text-muted-foreground'>
                      {school.admissionRate}%
                    </span>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Main Schools Page Component with Role-Based Rendering
export default function SchoolsPage() {
  const { user, loading, isAdmin, isStudent, isConsultant } = useCurrentUser();

  // Show loading state while determining user role
  if (loading) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <Card>
          <CardContent className='flex items-center justify-center py-12'>
            <div className='flex items-center gap-2'>
              <Icons.spinner className='h-4 w-4 animate-spin' />
              <span>Loading...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show access denied for unauthenticated users
  if (!user) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <Card>
          <CardContent className='flex items-center justify-center py-12'>
            <div className='text-center'>
              <Icons.lock className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
              <h3 className='mb-2 text-lg font-semibold'>Access Denied</h3>
              <p className='text-muted-foreground'>
                Please log in to access this page.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render appropriate component based on user role
  if (isAdmin) {
    return <AdminSchoolsManagement />;
  } else if (isStudent) {
    return <StudentSchoolsManagement />;
  } else if (isConsultant) {
    // For now, consultants see the student view
    // This could be customized later for consultant-specific needs
    return <StudentSchoolsManagement />;
  }

  // Fallback for unknown roles
  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <Card>
        <CardContent className='flex items-center justify-center py-12'>
          <div className='text-center'>
            <Icons.alertCircle className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
            <h3 className='mb-2 text-lg font-semibold'>Unknown Role</h3>
            <p className='text-muted-foreground'>
              Your user role is not recognized. Please contact an administrator.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

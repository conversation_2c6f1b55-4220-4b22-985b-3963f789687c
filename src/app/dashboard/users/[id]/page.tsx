'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import { useCurrentUser } from '@/hooks/use-current-user';

interface User {
  id: string;
  clerk_id: string;
  email: string;
  role: 'student' | 'consultant' | 'admin';
  profile_data?: any;
  created_at: string;
  updated_at: string;
}

export default function UserDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user: currentUser } = useCurrentUser();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const userId = params.id as string;

  useEffect(() => {
    fetchUser();
  }, [userId]);

  const fetchUser = async () => {
    try {
      const response = await fetch(`/api/users/${userId}`);
      const result = await response.json();

      if (result.success) {
        setUser(result.data);
      } else {
        toast.error('Failed to load user details');
        router.push('/dashboard/users/all');
      }
    } catch (error) {
      console.error('Error fetching user:', error);
      toast.error('Failed to load user details');
      router.push('/dashboard/users/all');
    } finally {
      setLoading(false);
    }
  };

  const getUserDisplayName = (user: User): string => {
    if (user.profile_data?.name) {
      return user.profile_data.name;
    }
    if (user.profile_data?.firstName && user.profile_data?.lastName) {
      return `${user.profile_data.firstName} ${user.profile_data.lastName}`;
    }
    return user.email.split('@')[0];
  };

  const getUserAvatar = (user: User): string | undefined => {
    return user.profile_data?.avatar || user.profile_data?.picture;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-center py-12">
          <Icons.spinner className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <h3 className="text-lg font-semibold">User not found</h3>
            <p className="text-muted-foreground">The user you're looking for doesn't exist.</p>
            <Button onClick={() => router.push('/dashboard/users/all')} className="mt-4">
              Back to Users
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const displayName = getUserDisplayName(user);
  const avatar = getUserAvatar(user);
  const isDeactivated = user.profile_data?.status === 'inactive';

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/dashboard/users/all')}
          >
            <Icons.arrowLeft className="mr-2 h-4 w-4" />
            Back to Users
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">User Details</h2>
            <p className="text-muted-foreground">
              View and manage user information
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/dashboard/users/${userId}/edit`)}
          >
            <Icons.edit className="mr-2 h-4 w-4" />
            Edit User
          </Button>
        </div>
      </div>

      {/* User Profile Card */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={avatar} />
                <AvatarFallback>
                  {displayName
                    .split(' ')
                    .map((n) => n[0])
                    .join('')
                    .toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="flex items-center gap-2">
                  <CardTitle className="text-2xl">{displayName}</CardTitle>
                  {isDeactivated && (
                    <Badge variant="destructive">Deactivated</Badge>
                  )}
                </div>
                <p className="text-muted-foreground">{user.email}</p>
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
                    {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Basic Information</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">User ID</p>
                <p className="font-mono">{user.id}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Clerk ID</p>
                <p className="font-mono">{user.clerk_id}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Email</p>
                <p>{user.email}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Role</p>
                <p>{user.role}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Created</p>
                <p>{formatDate(user.created_at)}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Last Updated</p>
                <p>{formatDate(user.updated_at)}</p>
              </div>
            </div>
          </div>

          {/* Profile Data */}
          {user.profile_data && Object.keys(user.profile_data).length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-3">Profile Data</h3>
              <div className="bg-muted p-4 rounded-lg">
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(user.profile_data, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

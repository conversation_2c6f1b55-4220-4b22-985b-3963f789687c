'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import { useCurrentUser } from '@/hooks/use-current-user';

interface User {
  id: string;
  clerk_id: string;
  email: string;
  role: 'student' | 'consultant' | 'admin';
  profile_data?: any;
  created_at: string;
  updated_at: string;
}

export default function EditUserPage() {
  const params = useParams();
  const router = useRouter();
  const { user: currentUser } = useCurrentUser();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    role: 'student' as 'student' | 'consultant' | 'admin',
    firstName: '',
    lastName: '',
    name: ''
  });

  const userId = params.id as string;

  useEffect(() => {
    fetchUser();
  }, [userId]);

  const fetchUser = async () => {
    try {
      const response = await fetch(`/api/users/${userId}`);
      const result = await response.json();

      if (result.success) {
        const userData = result.data;
        setUser(userData);
        setFormData({
          email: userData.email,
          role: userData.role,
          firstName: userData.profile_data?.firstName || '',
          lastName: userData.profile_data?.lastName || '',
          name: userData.profile_data?.name || ''
        });
      } else {
        toast.error('Failed to load user details');
        router.push('/dashboard/users/all');
      }
    } catch (error) {
      console.error('Error fetching user:', error);
      toast.error('Failed to load user details');
      router.push('/dashboard/users/all');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: formData.email,
          role: formData.role,
          profile_data: {
            ...user?.profile_data,
            firstName: formData.firstName,
            lastName: formData.lastName,
            name: formData.name
          }
        })
      });

      const result = await response.json();

      if (result.success) {
        toast.success('User updated successfully');
        router.push(`/dashboard/users/${userId}`);
      } else {
        toast.error(result.error || 'Failed to update user');
      }
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-center py-12">
          <Icons.spinner className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <h3 className="text-lg font-semibold">User not found</h3>
            <p className="text-muted-foreground">The user you're looking for doesn't exist.</p>
            <Button onClick={() => router.push('/dashboard/users/all')} className="mt-4">
              Back to Users
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Check if current user can edit this user
  const canEdit = currentUser?.role === 'admin' || currentUser?.id === userId;

  if (!canEdit) {
    return (
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <h3 className="text-lg font-semibold">Access Denied</h3>
            <p className="text-muted-foreground">You don't have permission to edit this user.</p>
            <Button onClick={() => router.push('/dashboard/users/all')} className="mt-4">
              Back to Users
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/dashboard/users/${userId}`)}
          >
            <Icons.arrowLeft className="mr-2 h-4 w-4" />
            Back to User
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Edit User</h2>
            <p className="text-muted-foreground">
              Update user information and settings
            </p>
          </div>
        </div>
      </div>

      {/* Edit Form */}
      <Card>
        <CardHeader>
          <CardTitle>User Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                value={formData.firstName}
                onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                placeholder="Enter first name"
              />
            </div>
            <div>
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                value={formData.lastName}
                onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                placeholder="Enter last name"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="name">Display Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Enter display name (optional)"
            />
          </div>

          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              placeholder="Enter email address"
            />
          </div>

          {currentUser?.role === 'admin' && (
            <div>
              <Label htmlFor="role">Role</Label>
              <Select
                value={formData.role}
                onValueChange={(value: 'student' | 'consultant' | 'admin') =>
                  setFormData({ ...formData, role: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="student">Student</SelectItem>
                  <SelectItem value="consultant">Consultant</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="flex gap-2 pt-4">
            <Button onClick={handleSave} disabled={saving}>
              {saving ? (
                <>
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Icons.save className="mr-2 h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push(`/dashboard/users/${userId}`)}
            >
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

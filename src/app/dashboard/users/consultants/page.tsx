'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Icons } from '@/components/icons';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

interface Consultant {
  id: string;
  name: string;
  email: string;
  status: 'active' | 'inactive' | 'pending';
  avatar?: string;
  createdAt: string;
  lastLoginAt?: string;
  profile: {
    title?: string;
    specializations: string[];
    experience?: string;
    education?: string;
    bio?: string;
  };
  stats: {
    studentsAssigned: number;
    activeStudents: number;
    documentsReviewed: number;
    meetingsHeld: number;
    avgRating: number;
    totalReviews: number;
  };
  availability: {
    hoursPerWeek: number;
    maxStudents: number;
  };
}

const mockConsultants: Consultant[] = [
  {
    id: '1',
    name: 'Dr. Emily Rodriguez',
    email: '<EMAIL>',
    status: 'active',
    createdAt: '2025-05-10T08:00:00Z',
    lastLoginAt: '2025-07-04T11:30:00Z',
    profile: {
      title: 'Senior College Admissions Consultant',
      specializations: ['Ivy League', 'STEM Programs', 'Pre-Med'],
      experience: '8 years',
      education: 'PhD in Education, Harvard University',
      bio: 'Former admissions officer at MIT with extensive experience in STEM applications.'
    },
    stats: {
      studentsAssigned: 12,
      activeStudents: 8,
      documentsReviewed: 156,
      meetingsHeld: 89,
      avgRating: 4.9,
      totalReviews: 45
    },
    availability: {
      hoursPerWeek: 30,
      maxStudents: 15
    }
  },
  {
    id: '2',
    name: 'David Kim',
    email: '<EMAIL>',
    status: 'active',
    createdAt: '2025-05-15T12:15:00Z',
    lastLoginAt: '2025-07-02T14:20:00Z',
    profile: {
      title: 'College Admissions Consultant',
      specializations: [
        'Liberal Arts',
        'Business Schools',
        'International Students'
      ],
      experience: '5 years',
      education: 'MBA, Wharton School',
      bio: 'Specializes in business school applications and international student guidance.'
    },
    stats: {
      studentsAssigned: 10,
      activeStudents: 6,
      documentsReviewed: 98,
      meetingsHeld: 67,
      avgRating: 4.7,
      totalReviews: 32
    },
    availability: {
      hoursPerWeek: 25,
      maxStudents: 12
    }
  },
  {
    id: '3',
    name: 'Sarah Thompson',
    email: '<EMAIL>',
    status: 'pending',
    createdAt: '2025-06-20T16:00:00Z',
    profile: {
      title: 'Junior College Admissions Consultant',
      specializations: ['Public Universities', 'Scholarship Applications'],
      experience: '2 years',
      education: 'MA in Counseling, UCLA'
    },
    stats: {
      studentsAssigned: 0,
      activeStudents: 0,
      documentsReviewed: 0,
      meetingsHeld: 0,
      avgRating: 0,
      totalReviews: 0
    },
    availability: {
      hoursPerWeek: 20,
      maxStudents: 8
    }
  }
];

const statusColors = {
  active: 'bg-green-100 text-green-800',
  inactive: 'bg-gray-100 text-gray-800',
  pending: 'bg-yellow-100 text-yellow-800'
};

export default function ConsultantsPage() {
  const router = useRouter();
  const [consultants, setConsultants] = useState<Consultant[]>(mockConsultants);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [specializationFilter, setSpecializationFilter] =
    useState<string>('all');

  const filteredConsultants = consultants.filter((consultant) => {
    const matchesSearch =
      consultant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      consultant.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      consultant.profile.specializations.some((s) =>
        s.toLowerCase().includes(searchTerm.toLowerCase())
      );
    const matchesStatus =
      statusFilter === 'all' || consultant.status === statusFilter;
    const matchesSpecialization =
      specializationFilter === 'all' ||
      consultant.profile.specializations.includes(specializationFilter);

    return matchesSearch && matchesStatus && matchesSpecialization;
  });

  const getStats = () => {
    const activeConsultants = consultants.filter((c) => c.status === 'active');
    return {
      total: consultants.length,
      active: activeConsultants.length,
      pending: consultants.filter((c) => c.status === 'pending').length,
      totalStudents: activeConsultants.reduce(
        (sum, c) => sum + c.stats.studentsAssigned,
        0
      ),
      avgRating:
        activeConsultants.length > 0
          ? (
              activeConsultants.reduce((sum, c) => sum + c.stats.avgRating, 0) /
              activeConsultants.length
            ).toFixed(1)
          : '0.0',
      totalCapacity: consultants.reduce(
        (sum, c) => sum + c.availability.maxStudents,
        0
      )
    };
  };

  const stats = getStats();
  const allSpecializations = Array.from(
    new Set(consultants.flatMap((c) => c.profile.specializations))
  );

  // Handler functions
  const handleAddConsultant = () => {
    router.push('/dashboard/invitations');
  };

  const handleExport = () => {
    toast.info('Export feature coming soon');
  };

  const handleViewProfile = (consultantId: string) => {
    router.push(`/dashboard/users/${consultantId}`);
  };

  const handleEditConsultant = (consultantId: string) => {
    router.push(`/dashboard/users/${consultantId}/edit`);
  };

  const handleManageStudents = (consultantId: string) => {
    // For now, redirect to students page with consultant filter
    router.push(`/dashboard/users/students?consultant=${consultantId}`);
  };

  const handleViewSchedule = (consultantId: string) => {
    toast.info('Schedule management feature coming soon');
  };

  const handleSendMessage = (consultantId: string) => {
    toast.info('Messaging feature coming soon');
  };

  const handleDeactivateConsultant = async (consultantId: string, consultantName: string) => {
    if (confirm(`Are you sure you want to deactivate ${consultantName}?`)) {
      try {
        const response = await fetch(`/api/users/${consultantId}/deactivate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          toast.success('Consultant deactivated successfully');
          // Refresh the page to update the list
          window.location.reload();
        } else {
          toast.error('Failed to deactivate consultant');
        }
      } catch (error) {
        console.error('Error deactivating consultant:', error);
        toast.error('Failed to deactivate consultant');
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatLastLogin = (dateString?: string) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return formatDate(dateString);
  };

  const getUtilizationColor = (current: number, max: number) => {
    const percentage = (current / max) * 100;
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 75) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Consultants</h1>
          <p className='text-muted-foreground'>
            Manage consultant accounts and monitor their performance
          </p>
        </div>
        <div className='flex gap-2'>
          <Button variant='outline' onClick={handleExport}>
            <Icons.download className='mr-2 h-4 w-4' />
            Export
          </Button>
          <Button onClick={handleAddConsultant}>
            <Icons.userPlus className='mr-2 h-4 w-4' />
            Add Consultant
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-6'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Consultants
            </CardTitle>
            <Icons.userCheck className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.total}</div>
            <p className='text-muted-foreground text-xs'>All consultants</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Active</CardTitle>
            <Icons.checkCircle className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.active}</div>
            <p className='text-muted-foreground text-xs'>Currently active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Pending</CardTitle>
            <Icons.clock className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.pending}</div>
            <p className='text-muted-foreground text-xs'>Awaiting approval</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Students Served
            </CardTitle>
            <Icons.users className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.totalStudents}</div>
            <p className='text-muted-foreground text-xs'>Currently assigned</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Avg Rating</CardTitle>
            <Icons.star className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.avgRating}</div>
            <p className='text-muted-foreground text-xs'>Out of 5.0</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Capacity
            </CardTitle>
            <Icons.target className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.totalCapacity}</div>
            <p className='text-muted-foreground text-xs'>Max students</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Consultants</CardTitle>
          <CardDescription>
            {filteredConsultants.length} of {consultants.length} consultants
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex flex-col gap-4 md:flex-row md:items-center'>
            <div className='flex-1'>
              <Input
                placeholder='Search consultants by name, email, or specialization...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='max-w-sm'
              />
            </div>
            <div className='flex gap-2'>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className='w-[120px]'>
                  <SelectValue placeholder='Status' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Status</SelectItem>
                  <SelectItem value='active'>Active</SelectItem>
                  <SelectItem value='inactive'>Inactive</SelectItem>
                  <SelectItem value='pending'>Pending</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={specializationFilter}
                onValueChange={setSpecializationFilter}
              >
                <SelectTrigger className='w-[160px]'>
                  <SelectValue placeholder='Specialization' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Specializations</SelectItem>
                  {allSpecializations.map((spec) => (
                    <SelectItem key={spec} value={spec}>
                      {spec}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Consultants List */}
      <div className='space-y-4'>
        {filteredConsultants.length === 0 ? (
          <Card>
            <CardContent className='flex flex-col items-center justify-center py-12'>
              <Icons.userCheck className='text-muted-foreground mb-4 h-12 w-12' />
              <h3 className='mb-2 text-lg font-semibold'>
                No consultants found
              </h3>
              <p className='text-muted-foreground mb-4 text-center'>
                {searchTerm ||
                statusFilter !== 'all' ||
                specializationFilter !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'No consultants registered yet'}
              </p>
              <Button onClick={handleAddConsultant}>
                <Icons.userPlus className='mr-2 h-4 w-4' />
                Add Consultant
              </Button>
            </CardContent>
          </Card>
        ) : (
          filteredConsultants.map((consultant) => (
            <Card
              key={consultant.id}
              className='transition-shadow hover:shadow-md'
            >
              <CardContent className='p-6'>
                <div className='flex items-start justify-between'>
                  <div className='flex-1'>
                    <div className='mb-3 flex items-center gap-3'>
                      <Avatar className='h-12 w-12'>
                        <AvatarImage src={consultant.avatar} />
                        <AvatarFallback>
                          {consultant.name
                            .split(' ')
                            .map((n) => n[0])
                            .join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className='text-lg font-semibold'>
                          {consultant.name}
                        </h3>
                        <p className='text-muted-foreground text-sm'>
                          {consultant.profile.title}
                        </p>
                        <p className='text-muted-foreground text-sm'>
                          {consultant.email}
                        </p>
                      </div>
                    </div>

                    <div className='mb-4 flex items-center gap-2'>
                      <Badge className={statusColors[consultant.status]}>
                        {consultant.status}
                      </Badge>
                      {consultant.profile.experience && (
                        <Badge variant='outline'>
                          {consultant.profile.experience} experience
                        </Badge>
                      )}
                      {consultant.stats.avgRating > 0 && (
                        <Badge variant='outline'>
                          <Icons.star className='mr-1 h-3 w-3 fill-current' />
                          {consultant.stats.avgRating} (
                          {consultant.stats.totalReviews})
                        </Badge>
                      )}
                    </div>

                    <div className='mb-4'>
                      <p className='mb-2 text-sm font-medium'>
                        Specializations
                      </p>
                      <div className='flex flex-wrap gap-1'>
                        {consultant.profile.specializations.map((spec) => (
                          <Badge
                            key={spec}
                            variant='secondary'
                            className='text-xs'
                          >
                            {spec}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className='mb-4 grid grid-cols-2 gap-4 md:grid-cols-5'>
                      <div className='text-center'>
                        <p className='text-lg font-bold'>
                          {consultant.stats.studentsAssigned}
                        </p>
                        <p className='text-muted-foreground text-xs'>
                          Students
                        </p>
                      </div>
                      <div className='text-center'>
                        <p className='text-lg font-bold'>
                          {consultant.stats.documentsReviewed}
                        </p>
                        <p className='text-muted-foreground text-xs'>
                          Documents
                        </p>
                      </div>
                      <div className='text-center'>
                        <p className='text-lg font-bold'>
                          {consultant.stats.meetingsHeld}
                        </p>
                        <p className='text-muted-foreground text-xs'>
                          Meetings
                        </p>
                      </div>
                      <div className='text-center'>
                        <p className='text-lg font-bold'>
                          {consultant.availability.hoursPerWeek}h
                        </p>
                        <p className='text-muted-foreground text-xs'>
                          Per week
                        </p>
                      </div>
                      <div className='text-center'>
                        <p
                          className={`text-lg font-bold ${getUtilizationColor(consultant.stats.studentsAssigned, consultant.availability.maxStudents)}`}
                        >
                          {consultant.stats.studentsAssigned}/
                          {consultant.availability.maxStudents}
                        </p>
                        <p className='text-muted-foreground text-xs'>
                          Capacity
                        </p>
                      </div>
                    </div>

                    <div className='text-muted-foreground flex items-center gap-4 text-sm'>
                      <div className='flex items-center gap-1'>
                        <Icons.calendar className='h-4 w-4' />
                        Joined {formatDate(consultant.createdAt)}
                      </div>
                      <div className='flex items-center gap-1'>
                        <Icons.clock className='h-4 w-4' />
                        Last login {formatLastLogin(consultant.lastLoginAt)}
                      </div>
                    </div>
                  </div>

                  <div className='ml-4 flex gap-2'>
                    <Button variant='outline' size='sm' onClick={() => handleViewProfile(consultant.id)}>
                      <Icons.eye className='mr-2 h-4 w-4' />
                      View Profile
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant='outline' size='sm'>
                          <Icons.moreHorizontal className='h-4 w-4' />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align='end'>
                        <DropdownMenuItem onClick={() => handleEditConsultant(consultant.id)}>
                          <Icons.edit className='mr-2 h-4 w-4' />
                          Edit Consultant
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleManageStudents(consultant.id)}>
                          <Icons.users className='mr-2 h-4 w-4' />
                          Manage Students
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleViewSchedule(consultant.id)}>
                          <Icons.calendar className='mr-2 h-4 w-4' />
                          View Schedule
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleSendMessage(consultant.id)}>
                          <Icons.messageSquare className='mr-2 h-4 w-4' />
                          Send Message
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDeactivateConsultant(consultant.id, consultant.name)}>
                          <Icons.userX className='mr-2 h-4 w-4' />
                          Deactivate
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Icons } from '@/components/icons';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import { useApiGet } from '@/lib/api-client';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useRouter } from 'next/navigation';

interface User {
  id: string;
  clerk_id: string;
  email: string;
  role: 'student' | 'consultant' | 'admin';
  profile_data?: any;
  created_at: string;
  updated_at: string;
  // Derived fields for display
  name?: string;
  status?: 'active' | 'inactive' | 'pending';
  avatar?: string;
  lastLoginAt?: string;
  documentsCount?: number;
  studentsAssigned?: number;
}

// Helper functions to derive display fields from user data
const getUserDisplayName = (user: User): string => {
  // Try to get name from profile_data first
  if (user.profile_data?.name) {
    return user.profile_data.name;
  }
  if (user.profile_data?.firstName && user.profile_data?.lastName) {
    return `${user.profile_data.firstName} ${user.profile_data.lastName}`;
  }
  // Fallback to email username
  return user.email.split('@')[0];
};

const getUserStatus = (user: User): 'active' | 'inactive' | 'pending' => {
  // For now, assume all users are active
  // This could be enhanced with actual status tracking
  return 'active';
};

const getUserAvatar = (user: User): string | undefined => {
  return user.profile_data?.avatar || user.profile_data?.picture;
};

const roleColors = {
  student: 'bg-blue-100 text-blue-800',
  consultant: 'bg-green-100 text-green-800',
  admin: 'bg-purple-100 text-purple-800'
};

const statusColors = {
  active: 'bg-green-100 text-green-800',
  inactive: 'bg-gray-100 text-gray-800',
  pending: 'bg-yellow-100 text-yellow-800'
};

export default function AllUsersPage() {
  const { user: currentUser, loading: userLoading, isAdmin } = useCurrentUser();
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');

  // Fetch real user data from API
  const {
    data: usersResponse,
    loading,
    error,
    fetch: fetchUsers
  } = useApiGet<any>('/api/users');

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // Show loading state while checking user role
  if (userLoading) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <Card>
          <CardContent className='flex items-center justify-center py-12'>
            <div className='flex items-center gap-2'>
              <Icons.spinner className='h-4 w-4 animate-spin' />
              <span>Loading...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show access denied for non-admin users
  if (!isAdmin) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <Icons.users className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
            <h3 className='text-lg font-semibold'>Access Restricted</h3>
            <p className='text-muted-foreground mt-2 text-sm'>
              This page is only available to administrators.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Handle paginated response structure
  const users: User[] = usersResponse?.data || [];
  const pagination = usersResponse?.pagination;

  const filteredUsers = users.filter((user) => {
    const displayName = getUserDisplayName(user);
    const status = getUserStatus(user);

    const matchesSearch =
      displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    const matchesStatus = statusFilter === 'all' || status === statusFilter;

    return matchesSearch && matchesRole && matchesStatus;
  });

  const getStats = () => {
    return {
      total: users.length,
      students: users.filter((u) => u.role === 'student').length,
      consultants: users.filter((u) => u.role === 'consultant').length,
      admins: users.filter((u) => u.role === 'admin').length,
      active: users.filter((u) => getUserStatus(u) === 'active').length,
      pending: users.filter((u) => getUserStatus(u) === 'pending').length
    };
  };

  const stats = getStats();

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatLastLogin = (dateString?: string) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return formatDate(dateString);
  };

  // Handler for Add User button
  const handleAddUser = () => {
    router.push('/dashboard/invitations');
  };

  const handleViewUser = (userId: string) => {
    router.push(`/dashboard/users/${userId}`);
  };

  const handleEditUser = (userId: string) => {
    // For now, redirect to user profile where they can edit their info
    router.push(`/dashboard/users/${userId}/edit`);
  };

  const handleDeactivateUser = async (userId: string, userEmail: string) => {
    if (confirm(`Are you sure you want to deactivate ${userEmail}?`)) {
      try {
        const response = await fetch(`/api/users/${userId}/deactivate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          toast.success('User deactivated successfully');
          // Refresh the users list
          window.location.reload();
        } else {
          toast.error('Failed to deactivate user');
        }
      } catch (error) {
        console.error('Error deactivating user:', error);
        toast.error('Failed to deactivate user');
      }
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className='space-y-6'>
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold tracking-tight'>All Users</h1>
            <p className='text-muted-foreground'>
              Loading user data...
            </p>
          </div>
        </div>
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardContent className='p-6'>
                <div className='animate-pulse space-y-2'>
                  <div className='h-4 bg-gray-200 rounded w-3/4'></div>
                  <div className='h-3 bg-gray-200 rounded w-1/2'></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className='space-y-6'>
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold tracking-tight'>All Users</h1>
            <p className='text-muted-foreground'>
              Error loading user data
            </p>
          </div>
        </div>
        <Card>
          <CardContent className='flex flex-col items-center justify-center py-12'>
            <Icons.alertTriangle className='h-12 w-12 text-red-500 mb-4' />
            <h3 className='text-lg font-semibold mb-2'>Failed to load users</h3>
            <p className='text-muted-foreground mb-4 text-center'>
              {error}
            </p>
            <Button onClick={() => fetchUsers()}>
              <Icons.arrowRight className='mr-2 h-4 w-4' />
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      {/* Header */}
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>All Users</h2>
          <p className='text-muted-foreground'>
            Manage all users across the platform ({users.length} total)
          </p>
        </div>
        <div className='flex gap-2'>
          <Button
            variant='outline'
            onClick={() =>
              setViewMode(viewMode === 'table' ? 'cards' : 'table')
            }
          >
            {viewMode === 'table' ? (
              <Icons.grid className='mr-2 h-4 w-4' />
            ) : (
              <Icons.list className='mr-2 h-4 w-4' />
            )}
            {viewMode === 'table' ? 'Card View' : 'Table View'}
          </Button>
          <Button onClick={handleAddUser}>
            <Icons.userPlus className='mr-2 h-4 w-4' />
            Add User
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-6'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Users</CardTitle>
            <Icons.users className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.total}</div>
            <p className='text-muted-foreground text-xs'>All platform users</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Students</CardTitle>
            <Icons.graduationCap className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.students}</div>
            <p className='text-muted-foreground text-xs'>Active students</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Consultants</CardTitle>
            <Icons.userCheck className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.consultants}</div>
            <p className='text-muted-foreground text-xs'>Active consultants</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Admins</CardTitle>
            <Icons.shield className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.admins}</div>
            <p className='text-muted-foreground text-xs'>System admins</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Active</CardTitle>
            <Icons.checkCircle className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.active}</div>
            <p className='text-muted-foreground text-xs'>Active users</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Pending</CardTitle>
            <Icons.clock className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.pending}</div>
            <p className='text-muted-foreground text-xs'>Awaiting approval</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Users</CardTitle>
          <CardDescription>
            {filteredUsers.length} of {users.length} users
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex flex-col gap-4 md:flex-row md:items-center'>
            <div className='flex-1'>
              <Input
                placeholder='Search users by name or email...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='max-w-sm'
              />
            </div>
            <div className='flex gap-2'>
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className='w-[140px]'>
                  <SelectValue placeholder='All Roles' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Roles</SelectItem>
                  <SelectItem value='student'>Students</SelectItem>
                  <SelectItem value='consultant'>Consultants</SelectItem>
                  <SelectItem value='admin'>Admins</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className='w-[140px]'>
                  <SelectValue placeholder='All Status' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Status</SelectItem>
                  <SelectItem value='active'>Active</SelectItem>
                  <SelectItem value='inactive'>Inactive</SelectItem>
                  <SelectItem value='pending'>Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Display */}
      {viewMode === 'table' ? (
        <Card>
          <CardContent className='p-0'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Joined</TableHead>
                  <TableHead>Last Login</TableHead>
                  <TableHead>Activity</TableHead>
                  <TableHead className='w-[70px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className='py-12 text-center'>
                      <div className='flex flex-col items-center'>
                        <Icons.users className='text-muted-foreground mb-4 h-12 w-12' />
                        <h3 className='mb-2 text-lg font-semibold'>
                          No users found
                        </h3>
                        <p className='text-muted-foreground'>
                          {searchTerm ||
                          roleFilter !== 'all' ||
                          statusFilter !== 'all'
                            ? 'Try adjusting your search or filters'
                            : 'No users available'}
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user) => {
                    const displayName = getUserDisplayName(user);
                    const status = getUserStatus(user);
                    const avatar = getUserAvatar(user);

                    return (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div className='flex items-center gap-3'>
                            <Avatar className='h-8 w-8'>
                              <AvatarImage src={avatar} />
                              <AvatarFallback>
                                {displayName
                                  .split(' ')
                                  .map((n) => n[0])
                                  .join('')
                                  .toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className='font-medium'>{displayName}</div>
                              <div className='text-muted-foreground text-sm'>
                                {user.email}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={roleColors[user.role]}>
                            {user.role}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={statusColors[status]}>
                            {status}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatDate(user.created_at)}</TableCell>
                        <TableCell>{formatLastLogin(user.lastLoginAt)}</TableCell>
                      <TableCell>
                        {user.role === 'student' &&
                          user.documentsCount !== undefined && (
                            <span className='text-muted-foreground text-sm'>
                              {user.documentsCount} documents
                            </span>
                          )}
                        {user.role === 'consultant' &&
                          user.studentsAssigned !== undefined && (
                            <span className='text-muted-foreground text-sm'>
                              {user.studentsAssigned} students
                            </span>
                          )}
                        {user.role === 'admin' && (
                          <span className='text-muted-foreground text-sm'>
                            System admin
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant='ghost' size='sm'>
                              <Icons.moreHorizontal className='h-4 w-4' />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align='end'>
                            <DropdownMenuItem onClick={() => handleViewUser(user.id)}>
                              <Icons.eye className='mr-2 h-4 w-4' />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditUser(user.id)}>
                              <Icons.edit className='mr-2 h-4 w-4' />
                              Edit User
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDeactivateUser(user.id, user.email)}>
                              <Icons.userX className='mr-2 h-4 w-4' />
                              Deactivate
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      ) : (
        /* Card View */
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
          {filteredUsers.length === 0 ? (
            <Card className='md:col-span-2 lg:col-span-3'>
              <CardContent className='flex flex-col items-center justify-center py-12'>
                <Icons.users className='text-muted-foreground mb-4 h-12 w-12' />
                <h3 className='mb-2 text-lg font-semibold'>No users found</h3>
                <p className='text-muted-foreground mb-4 text-center'>
                  {searchTerm || roleFilter !== 'all' || statusFilter !== 'all'
                    ? 'Try adjusting your search or filters'
                    : 'No users available'}
                </p>
                <Button onClick={handleAddUser}>
                  <Icons.userPlus className='mr-2 h-4 w-4' />
                  Add User
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredUsers.map((user) => {
              const displayName = getUserDisplayName(user);
              const status = getUserStatus(user);
              const avatar = getUserAvatar(user);

              return (
                <Card key={user.id} className='transition-shadow hover:shadow-md'>
                  <CardContent className='p-6'>
                    <div className='mb-4 flex items-start justify-between'>
                      <div className='flex items-center gap-3'>
                        <Avatar className='h-10 w-10'>
                          <AvatarImage src={avatar} />
                          <AvatarFallback>
                            {displayName
                              .split(' ')
                              .map((n) => n[0])
                              .join('')
                              .toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className='font-semibold'>{displayName}</h3>
                          <p className='text-muted-foreground text-sm'>
                            {user.email}
                          </p>
                        </div>
                      </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant='ghost' size='sm'>
                          <Icons.moreHorizontal className='h-4 w-4' />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align='end'>
                        <DropdownMenuItem onClick={() => handleViewUser(user.id)}>
                          <Icons.eye className='mr-2 h-4 w-4' />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditUser(user.id)}>
                          <Icons.edit className='mr-2 h-4 w-4' />
                          Edit User
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDeactivateUser(user.id, user.email)}>
                          <Icons.userX className='mr-2 h-4 w-4' />
                          Deactivate
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  <div className='mb-3 flex items-center gap-2'>
                    <Badge className={roleColors[user.role]}>{user.role}</Badge>
                    <Badge className={statusColors[status]}>
                      {status}
                    </Badge>
                  </div>

                  <div className='text-muted-foreground space-y-2 text-sm'>
                    <div className='flex items-center gap-2'>
                      <Icons.calendar className='h-4 w-4' />
                      Joined {formatDate(user.created_at)}
                    </div>
                    <div className='flex items-center gap-2'>
                      <Icons.clock className='h-4 w-4' />
                      Last login {formatLastLogin(user.lastLoginAt)}
                    </div>
                    {user.role === 'student' &&
                      user.documentsCount !== undefined && (
                        <div className='flex items-center gap-2'>
                          <Icons.fileText className='h-4 w-4' />
                          {user.documentsCount} documents
                        </div>
                      )}
                    {user.role === 'consultant' &&
                      user.studentsAssigned !== undefined && (
                        <div className='flex items-center gap-2'>
                          <Icons.users className='h-4 w-4' />
                          {user.studentsAssigned} students assigned
                        </div>
                      )}
                  </div>
                </CardContent>
              </Card>
              );
            })
          )}
        </div>
      )}
    </div>
  );
}

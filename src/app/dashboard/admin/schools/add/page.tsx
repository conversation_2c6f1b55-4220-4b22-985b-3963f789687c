'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useApiPost } from '@/lib/api-client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import type { SchoolDetails, ApplicationRequirements } from '@/types/application';

const schoolTypes = [
  'Public University',
  'Private University',
  'Liberal Arts College',
  'Community College',
  'Technical College',
  'Art School',
  'Music Conservatory',
  'Research University',
  'State College'
];

interface SchoolFormData {
  name: string;
  details: SchoolDetails;
  application_requirements: ApplicationRequirements;
}

export default function AdminAddSchoolPage() {
  const router = useRouter();
  const { isAdmin } = useCurrentUser();
  const [loading, setLoading] = useState(false);
  const { post: createSchool } = useApiPost();

  const [formData, setFormData] = useState<SchoolFormData>({
    name: '',
    details: {
      location: '',
      type: '',
      ranking: undefined,
      acceptance_rate: undefined,
      tuition: undefined,
      website: ''
    },
    application_requirements: {
      essays: [],
      test_scores_required: ['SAT', 'ACT'],
      transcript_required: true,
      recommendation_letters: 2,
      deadlines: {
        early_decision: '',
        early_action: '',
        regular_decision: ''
      }
    }
  });

  // Redirect if not admin
  if (!isAdmin) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <Icons.shield className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
            <h3 className='text-lg font-semibold'>Access Restricted</h3>
            <p className='text-muted-foreground mt-2 text-sm'>
              This page is only available to administrators.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const handleInputChange = (field: string, value: any) => {
    if (field.startsWith('details.')) {
      const detailField = field.replace('details.', '');
      setFormData(prev => ({
        ...prev,
        details: {
          ...prev.details,
          [detailField]: value
        }
      }));
    } else if (field.startsWith('application_requirements.')) {
      const reqField = field.replace('application_requirements.', '');
      if (reqField.startsWith('deadlines.')) {
        const deadlineField = reqField.replace('deadlines.', '');
        setFormData(prev => ({
          ...prev,
          application_requirements: {
            ...prev.application_requirements,
            deadlines: {
              ...prev.application_requirements.deadlines,
              [deadlineField]: value
            }
          }
        }));
      } else {
        setFormData(prev => ({
          ...prev,
          application_requirements: {
            ...prev.application_requirements,
            [reqField]: value
          }
        }));
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('School name is required');
      return;
    }

    setLoading(true);
    
    try {
      const response = await createSchool('/api/schools', formData);
      
      if (response.success) {
        toast.success('School created successfully!');
        router.push('/dashboard/schools');
      } else {
        toast.error('Failed to create school');
      }
    } catch (error) {
      console.error('Error creating school:', error);
      toast.error('Failed to create school');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>Add New School</h2>
          <p className='text-muted-foreground'>
            Create a new school in the database for students to add to their target lists
          </p>
        </div>
        <Button variant='outline' onClick={() => router.back()}>
          <Icons.chevronLeft className='mr-2 h-4 w-4' />
          Back
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>School Information</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className='space-y-6'>
            {/* Basic Information */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>Basic Information</h3>
              
              <div className='space-y-2'>
                <Label htmlFor='name'>School Name *</Label>
                <Input
                  id='name'
                  placeholder='e.g., Harvard University'
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  required
                />
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label htmlFor='location'>Location</Label>
                  <Input
                    id='location'
                    placeholder='e.g., Cambridge, MA'
                    value={formData.details.location || ''}
                    onChange={(e) => handleInputChange('details.location', e.target.value)}
                  />
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='type'>School Type</Label>
                  <Select
                    value={formData.details.type || ''}
                    onValueChange={(value) => handleInputChange('details.type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select school type' />
                    </SelectTrigger>
                    <SelectContent>
                      {schoolTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                <div className='space-y-2'>
                  <Label htmlFor='ranking'>Ranking (Optional)</Label>
                  <Input
                    id='ranking'
                    type='number'
                    placeholder='e.g., 1'
                    value={formData.details.ranking || ''}
                    onChange={(e) => handleInputChange('details.ranking', e.target.value ? parseInt(e.target.value) : undefined)}
                  />
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='acceptance_rate'>Acceptance Rate (%)</Label>
                  <Input
                    id='acceptance_rate'
                    type='number'
                    step='0.1'
                    placeholder='e.g., 5.2'
                    value={formData.details.acceptance_rate || ''}
                    onChange={(e) => handleInputChange('details.acceptance_rate', e.target.value ? parseFloat(e.target.value) : undefined)}
                  />
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='tuition'>Annual Tuition ($)</Label>
                  <Input
                    id='tuition'
                    type='number'
                    placeholder='e.g., 55000'
                    value={formData.details.tuition || ''}
                    onChange={(e) => handleInputChange('details.tuition', e.target.value ? parseInt(e.target.value) : undefined)}
                  />
                </div>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='website'>Website</Label>
                <Input
                  id='website'
                  type='url'
                  placeholder='e.g., https://www.harvard.edu'
                  value={formData.details.website || ''}
                  onChange={(e) => handleInputChange('details.website', e.target.value)}
                />
              </div>
            </div>

            {/* Application Requirements */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>Application Requirements</h3>
              
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label htmlFor='recommendation_letters'>Recommendation Letters</Label>
                  <Input
                    id='recommendation_letters'
                    type='number'
                    placeholder='e.g., 2'
                    value={formData.application_requirements.recommendation_letters || ''}
                    onChange={(e) => handleInputChange('application_requirements.recommendation_letters', e.target.value ? parseInt(e.target.value) : 0)}
                  />
                </div>
              </div>

              <div className='space-y-4'>
                <h4 className='font-medium'>Application Deadlines</h4>
                <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='early_decision'>Early Decision</Label>
                    <Input
                      id='early_decision'
                      type='date'
                      value={formData.application_requirements.deadlines?.early_decision || ''}
                      onChange={(e) => handleInputChange('application_requirements.deadlines.early_decision', e.target.value)}
                    />
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='early_action'>Early Action</Label>
                    <Input
                      id='early_action'
                      type='date'
                      value={formData.application_requirements.deadlines?.early_action || ''}
                      onChange={(e) => handleInputChange('application_requirements.deadlines.early_action', e.target.value)}
                    />
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='regular_decision'>Regular Decision</Label>
                    <Input
                      id='regular_decision'
                      type='date'
                      value={formData.application_requirements.deadlines?.regular_decision || ''}
                      onChange={(e) => handleInputChange('application_requirements.deadlines.regular_decision', e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className='flex gap-4'>
              <Button type='submit' disabled={loading}>
                {loading ? (
                  <>
                    <Icons.spinner className='mr-2 h-4 w-4 animate-spin' />
                    Creating School...
                  </>
                ) : (
                  <>
                    <Icons.plus className='mr-2 h-4 w-4' />
                    Create School
                  </>
                )}
              </Button>
              <Button type='button' variant='outline' onClick={() => router.back()}>
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
